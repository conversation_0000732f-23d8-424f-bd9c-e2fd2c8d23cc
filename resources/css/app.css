/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

/* Import Bootstrap */
@import 'bootstrap/dist/css/bootstrap.min.css';

/* Luxury Color Palette */
:root {
    --primary-gold: #D4AF37;
    --charcoal-black: #2C2C2C;
    --royal-blue: #1E3A8A;
    --emerald-green: #059669;
    --wine-red: #7C2D12;
    --midnight-blue: #1E293B;
    --light-cream: #FEFDF8;
    --pale-gray: #F8F9FA;
    --luxury-white: #FFFFFF;
}

/* Base Typography */
body {
    font-family: 'Montserrat', sans-serif;
    font-weight: 400;
    line-height: 1.6;
    color: var(--charcoal-black);
    background-color: var(--luxury-white);
}

/* Luxury Headings - Didot-style serif */
h1, h2, h3, h4, h5, h6,
.heading-font {
    font-family: 'Didot', 'Times New Roman', serif;
    font-weight: 400;
    line-height: 1.2;
    color: var(--charcoal-black);
    margin-bottom: 1rem;
}

h1 { font-size: 3.5rem; }
h2 { font-size: 2.8rem; }
h3 { font-size: 2.2rem; }
h4 { font-size: 1.8rem; }
h5 { font-size: 1.4rem; }
h6 { font-size: 1.2rem; }

/* Luxury Buttons */
.btn-luxury-primary {
    background: linear-gradient(135deg, var(--primary-gold), #B8860B);
    border: none;
    color: white;
    padding: 12px 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-radius: 0;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.btn-luxury-primary:hover {
    background: linear-gradient(135deg, #B8860B, var(--primary-gold));
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
    color: white;
}

.btn-luxury-secondary {
    background: transparent;
    border: 2px solid var(--charcoal-black);
    color: var(--charcoal-black);
    padding: 12px 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-radius: 0;
    transition: all 0.3s ease;
}

.btn-luxury-secondary:hover {
    background: var(--charcoal-black);
    color: white;
    transform: translateY(-2px);
}

/* Luxury Navbar */
.navbar-luxury {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(212, 175, 55, 0.2);
    transition: all 0.3s ease;
    padding: 1rem 0;
}

.navbar-luxury.scrolled {
    padding: 0.5rem 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-family: 'Didot', 'Times New Roman', serif;
    font-size: 2rem;
    font-weight: 400;
    color: var(--charcoal-black) !important;
    text-decoration: none;
}

.nav-link {
    font-weight: 500;
    color: var(--charcoal-black) !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0 10px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-gold) !important;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 50%;
    background: var(--primary-gold);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::after {
    width: 100%;
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, rgba(0,0,0,0.4), rgba(0,0,0,0.6));
    overflow: hidden;
}

.hero-video,
.hero-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1;
}

.hero-content {
    position: relative;
    z-index: 2;
    color: white;
    text-align: center;
}

.hero-title {
    font-size: 4rem;
    font-weight: 300;
    margin-bottom: 1.5rem;
    opacity: 0;
    animation: fadeInUp 1s ease 0.5s forwards;
}

.hero-subtitle {
    font-size: 1.3rem;
    font-weight: 300;
    margin-bottom: 2rem;
    opacity: 0;
    animation: fadeInUp 1s ease 0.8s forwards;
}

/* Enhanced Hero Section */
.hero-section-enhanced {
    position: relative;
    min-height: 100vh;
    overflow: hidden;
    display: flex;
    align-items: center;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0,0,0,0.6), rgba(0,0,0,0.4));
    z-index: 2;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
    pointer-events: none;
}

.floating-element {
    position: absolute;
    color: rgba(212, 175, 55, 0.3);
    font-size: 2rem;
    animation: float 6s ease-in-out infinite;
}

.floating-element:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-element:nth-child(2) {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.floating-element:nth-child(3) {
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}

.hero-content-enhanced {
    position: relative;
    z-index: 4;
    color: white;
    text-align: center;
}

.hero-badge {
    display: inline-block;
    background: rgba(212, 175, 55, 0.2);
    border: 1px solid var(--primary-gold);
    padding: 8px 20px;
    border-radius: 25px;
    margin-bottom: 2rem;
    backdrop-filter: blur(10px);
}

.badge-text {
    color: var(--primary-gold);
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hero-title-enhanced {
    font-size: 4.5rem;
    font-weight: 300;
    margin-bottom: 1.5rem;
    color: white;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
    line-height: 1.2;
}

.typewriter {
    display: inline-block;
    border-right: 3px solid var(--primary-gold);
    animation: blink 1s infinite;
}

.hero-subtitle-enhanced {
    font-size: 1.3rem;
    margin-bottom: 3rem;
    color: rgba(255,255,255,0.9);
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.hero-stats {
    margin-bottom: 3rem;
}

.stat-item {
    padding: 1rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-gold);
    font-family: var(--heading-font);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(255,255,255,0.8);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hero-cta {
    margin-bottom: 4rem;
}

.btn-luxury-primary-enhanced,
.btn-luxury-secondary-enhanced {
    position: relative;
    padding: 15px 35px;
    font-size: 1.1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-radius: 50px;
    overflow: hidden;
    transition: all 0.4s ease;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.btn-luxury-primary-enhanced {
    background: linear-gradient(135deg, var(--primary-gold), #B8860B);
    border: 2px solid var(--primary-gold);
    color: white;
}

.btn-luxury-primary-enhanced:hover {
    background: linear-gradient(135deg, #B8860B, var(--primary-gold));
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(212, 175, 55, 0.4);
    color: white;
}

.btn-luxury-secondary-enhanced {
    background: transparent;
    border: 2px solid white;
    color: white;
}

.btn-luxury-secondary-enhanced:hover {
    background: white;
    color: var(--charcoal-black);
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(255, 255, 255, 0.3);
}

.btn-icon {
    transition: transform 0.3s ease;
}

.btn-luxury-primary-enhanced:hover .btn-icon,
.btn-luxury-secondary-enhanced:hover .btn-icon {
    transform: translateX(5px);
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: rgba(255,255,255,0.8);
}

.scroll-text {
    font-size: 0.9rem;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.scroll-arrow {
    animation: bounce 2s infinite;
}

/* Search Bar Section */
.search-bar-section {
    margin-top: -80px;
    position: relative;
    z-index: 10;
    padding-bottom: 4rem;
}

.search-bar-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 20px 60px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.property-search-form .form-label {
    font-weight: 600;
    color: var(--charcoal-black);
    margin-bottom: 0.5rem;
}

.property-search-form .form-select {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.property-search-form .form-select:focus {
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 0.2rem rgba(212, 175, 55, 0.25);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
    50% { transform: translateY(-20px) rotate(5deg); opacity: 0.6; }
}

@keyframes blink {
    0%, 50% { border-color: var(--primary-gold); }
    51%, 100% { border-color: transparent; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.animate-fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1s ease forwards;
}

.animate-fade-in-up[data-delay="0.2s"] { animation-delay: 0.2s; }
.animate-fade-in-up[data-delay="0.4s"] { animation-delay: 0.4s; }
.animate-fade-in-up[data-delay="0.6s"] { animation-delay: 0.6s; }
.animate-fade-in-up[data-delay="0.8s"] { animation-delay: 0.8s; }
.animate-fade-in-up[data-delay="1.0s"] { animation-delay: 1.0s; }
.animate-fade-in-up[data-delay="1.2s"] { animation-delay: 1.2s; }

.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Property Cards */
.property-card {
    background: white;
    border-radius: 0;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    margin-bottom: 2rem;
}

.property-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.property-image {
    height: 300px;
    overflow: hidden;
    position: relative;
}

.property-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.property-card:hover .property-image img {
    transform: scale(1.05);
}

.property-price {
    position: absolute;
    top: 20px;
    right: 20px;
    background: var(--primary-gold);
    color: white;
    padding: 8px 15px;
    font-weight: 600;
    font-size: 1.1rem;
}

.property-content {
    padding: 2rem;
}

.property-title {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--charcoal-black);
}

.property-location {
    color: #666;
    margin-bottom: 1rem;
    font-size: 0.95rem;
}

.property-features {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    font-size: 0.9rem;
}

/* Luxury Sections */
.luxury-section {
    padding: 5rem 0;
    position: relative;
}

.luxury-section.bg-light {
    background: var(--light-cream);
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
}

.section-subtitle {
    color: var(--primary-gold);
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 1rem;
}

/* Parallax Effect */
.parallax-section {
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
}

.parallax-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
}

/* WhatsApp Float Button */
.whatsapp-float-container {
    position: fixed;
    bottom: 40px;
    right: 40px;
    z-index: 1000;
}

.whatsapp-float {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #25d366, #128c7e);
    color: #FFF;
    border-radius: 50px;
    text-align: center;
    font-size: 30px;
    box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    animation: whatsappPulse 2s infinite;
}

.whatsapp-float:hover {
    background: linear-gradient(135deg, #128c7e, #25d366);
    transform: scale(1.1);
    color: white;
    text-decoration: none;
    box-shadow: 0 6px 25px rgba(37, 211, 102, 0.6);
    animation: none;
}

.whatsapp-tooltip {
    position: absolute;
    bottom: 70px;
    right: 0;
    background: var(--charcoal-black);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 14px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    transform: translateY(10px);
}

.whatsapp-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    right: 20px;
    border: 5px solid transparent;
    border-top-color: var(--charcoal-black);
}

.whatsapp-float:hover + .whatsapp-tooltip {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

@keyframes whatsappPulse {
    0% {
        box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4);
    }
    50% {
        box-shadow: 0 4px 20px rgba(37, 211, 102, 0.8);
    }
    100% {
        box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    h1 { font-size: 2.5rem; }
    h2 { font-size: 2rem; }
    h3 { font-size: 1.7rem; }

    .navbar-brand {
        font-size: 1.5rem;
    }

    .property-features {
        flex-direction: column;
        gap: 0.5rem;
    }

    .whatsapp-float {
        width: 50px;
        height: 50px;
        bottom: 20px;
        right: 20px;
        font-size: 24px;
    }
}

/* About Hero Section */
.about-hero-section {
    position: relative;
    min-height: 75vh;
    overflow: hidden;
    display: flex;
    align-items: center;
}

.min-vh-75 {
    min-height: 75vh;
}

/* CTA Section with Background */
.cta-section-bg {
    position: relative;
    background-image: url('/images/cta-bg.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
}

.cta-section-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.7), rgba(0,0,0,0.5));
    z-index: 1;
}

.cta-section-bg .container {
    position: relative;
    z-index: 2;
}
