@extends('layouts.app')

@section('title', 'Contact Us - Luxury Real Estate Experts')
@section('description', 'Get in touch with our luxury real estate experts. Contact us for property inquiries, investment advice, and personalized assistance.')

@section('content')
<!-- Page Header -->
<section class="luxury-section bg-light" style="padding-top: 120px;">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title fade-in">
                    <p class="section-subtitle">Get In Touch</p>
                    <h1 class="heading-font">Contact Us</h1>
                    <p class="lead">Ready to find your dream luxury property? Let's start the conversation.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="luxury-section">
    <div class="container">
        <div class="row">
            <!-- Contact Form -->
            <div class="col-lg-8 mb-5">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-primary text-white" style="background: var(--primary-gold) !important;">
                        <h4 class="mb-0">
                            <i class="fas fa-envelope me-2"></i>Send Us a Message
                        </h4>
                    </div>
                    <div class="card-body p-4">
                        @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        @endif

                        @if($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>Please correct the errors below.
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        @endif

                        <form action="{{ route('contact.store') }}" method="POST">
                            @csrf
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">Full Name *</label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name') }}" required>
                                    @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                           id="email" name="email" value="{{ old('email') }}" required>
                                    @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                                           id="phone" name="phone" value="{{ old('phone') }}">
                                    @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="subject" class="form-label">Subject</label>
                                    <select class="form-select @error('subject') is-invalid @enderror" id="subject" name="subject">
                                        <option value="">Select Subject</option>
                                        <option value="Property Inquiry" {{ old('subject') == 'Property Inquiry' ? 'selected' : '' }}>Property Inquiry</option>
                                        <option value="Investment Consultation" {{ old('subject') == 'Investment Consultation' ? 'selected' : '' }}>Investment Consultation</option>
                                        <option value="NRI Services" {{ old('subject') == 'NRI Services' ? 'selected' : '' }}>NRI Services</option>
                                        <option value="Property Management" {{ old('subject') == 'Property Management' ? 'selected' : '' }}>Property Management</option>
                                        <option value="General Inquiry" {{ old('subject') == 'General Inquiry' ? 'selected' : '' }}>General Inquiry</option>
                                    </select>
                                    @error('subject')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="message" class="form-label">Message *</label>
                                <textarea class="form-control @error('message') is-invalid @enderror" 
                                          id="message" name="message" rows="5" required 
                                          placeholder="Tell us about your requirements, budget, preferred location, or any specific questions you have...">{{ old('message') }}</textarea>
                                @error('message')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="budget_range" class="form-label">Budget Range</label>
                                    <select class="form-select" id="budget_range" name="budget_range">
                                        <option value="">Select Budget</option>
                                        <option value="50L-1Cr" {{ old('budget_range') == '50L-1Cr' ? 'selected' : '' }}>₹50 Lakh - ₹1 Crore</option>
                                        <option value="1Cr-2Cr" {{ old('budget_range') == '1Cr-2Cr' ? 'selected' : '' }}>₹1 Crore - ₹2 Crore</option>
                                        <option value="2Cr-5Cr" {{ old('budget_range') == '2Cr-5Cr' ? 'selected' : '' }}>₹2 Crore - ₹5 Crore</option>
                                        <option value="5Cr-10Cr" {{ old('budget_range') == '5Cr-10Cr' ? 'selected' : '' }}>₹5 Crore - ₹10 Crore</option>
                                        <option value="10Cr+" {{ old('budget_range') == '10Cr+' ? 'selected' : '' }}>₹10 Crore+</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="preferred_location" class="form-label">Preferred Location</label>
                                    <input type="text" class="form-control" id="preferred_location" 
                                           name="preferred_location" value="{{ old('preferred_location') }}" 
                                           placeholder="e.g., Mumbai, Delhi, Bangalore">
                                </div>
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="newsletter" 
                                       name="newsletter_subscription" value="1" {{ old('newsletter_subscription') ? 'checked' : '' }}>
                                <label class="form-check-label" for="newsletter">
                                    Subscribe to our newsletter for latest property updates and market insights
                                </label>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-luxury-primary">
                                    <i class="fas fa-paper-plane me-2"></i>Send Message
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="col-lg-4">
                <!-- Contact Details -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>Contact Information
                        </h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="mb-4">
                            <div class="d-flex align-items-start">
                                <i class="fas fa-map-marker-alt fa-lg me-3 mt-1" style="color: var(--primary-gold);"></i>
                                <div>
                                    <h6 class="fw-bold">Office Address</h6>
                                    <p class="text-muted mb-0">123 Luxury Avenue<br>Premium Business District<br>Mumbai, Maharashtra 400001</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="d-flex align-items-start">
                                <i class="fas fa-phone fa-lg me-3 mt-1" style="color: var(--primary-gold);"></i>
                                <div>
                                    <h6 class="fw-bold">Phone Numbers</h6>
                                    <p class="text-muted mb-1">
                                        <a href="tel:+919876543210" class="text-decoration-none">+91 98765 43210</a> (Sales)
                                    </p>
                                    <p class="text-muted mb-0">
                                        <a href="tel:+919876543211" class="text-decoration-none">+91 98765 43211</a> (Support)
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="d-flex align-items-start">
                                <i class="fas fa-envelope fa-lg me-3 mt-1" style="color: var(--primary-gold);"></i>
                                <div>
                                    <h6 class="fw-bold">Email Addresses</h6>
                                    <p class="text-muted mb-1">
                                        <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                                    </p>
                                    <p class="text-muted mb-0">
                                        <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="d-flex align-items-start">
                                <i class="fas fa-clock fa-lg me-3 mt-1" style="color: var(--primary-gold);"></i>
                                <div>
                                    <h6 class="fw-bold">Business Hours</h6>
                                    <p class="text-muted mb-1">Monday - Saturday: 9:00 AM - 7:00 PM</p>
                                    <p class="text-muted mb-0">Sunday: 10:00 AM - 5:00 PM</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex gap-3 mt-4">
                            <a href="https://wa.me/919876543210" target="_blank" class="btn btn-success flex-fill">
                                <i class="fab fa-whatsapp me-1"></i>WhatsApp
                            </a>
                            <a href="tel:+919876543210" class="btn btn-luxury-primary flex-fill">
                                <i class="fas fa-phone me-1"></i>Call Now
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Links -->
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-link me-2"></i>Quick Links
                        </h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="d-grid gap-2">
                            <a href="{{ route('schedule-visit') }}" class="btn btn-outline-primary">
                                <i class="fas fa-calendar me-2"></i>Schedule Property Visit
                            </a>
                            <a href="{{ route('properties.index') }}" class="btn btn-outline-primary">
                                <i class="fas fa-home me-2"></i>Browse Properties
                            </a>
                            <a href="{{ route('services') }}" class="btn btn-outline-primary">
                                <i class="fas fa-concierge-bell me-2"></i>Our Services
                            </a>
                            <a href="{{ route('virtual-tour') }}" class="btn btn-outline-primary">
                                <i class="fas fa-vr-cardboard me-2"></i>Virtual Tours
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="py-0">
    <div class="container-fluid px-0">
        <div class="row g-0">
            <div class="col-12">
                <div id="contactMap" style="height: 400px; background: #f8f9fa;">
                    <!-- Google Maps will be embedded here -->
                    <div class="d-flex align-items-center justify-content-center h-100">
                        <div class="text-center">
                            <i class="fas fa-map-marked-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Our Office Location</h5>
                            <p class="text-muted">123 Luxury Avenue, Premium Business District, Mumbai</p>
                            <a href="https://www.google.com/maps/search/?api=1&query=123+Luxury+Avenue+Mumbai"
                               target="_blank" class="btn btn-outline-primary">
                                <i class="fas fa-external-link-alt me-2"></i>View on Google Maps
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="luxury-section bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title fade-in">
                    <p class="section-subtitle">Common Questions</p>
                    <h2 class="heading-font">Frequently Asked Questions</h2>
                </div>
            </div>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq1">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1" aria-expanded="true" aria-controls="collapse1">
                                How do I schedule a property visit?
                            </button>
                        </h2>
                        <div id="collapse1" class="accordion-collapse collapse show" aria-labelledby="faq1" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                You can schedule a property visit by clicking the "Schedule Visit" button on any property page, calling us directly, or using our contact form. We offer both physical and virtual tours at your convenience.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq2">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2" aria-expanded="false" aria-controls="collapse2">
                                What services do you provide for NRI clients?
                            </button>
                        </h2>
                        <div id="collapse2" class="accordion-collapse collapse" aria-labelledby="faq2" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Our NRI desk provides specialized services including remote property tours, FEMA compliance assistance, power of attorney services, property management, and complete transaction support from abroad.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq3">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3" aria-expanded="false" aria-controls="collapse3">
                                Do you provide investment advice?
                            </button>
                        </h2>
                        <div id="collapse3" class="accordion-collapse collapse" aria-labelledby="faq3" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Yes, we provide comprehensive investment advice including market analysis, ROI calculations, portfolio management, and strategic investment planning to maximize your returns.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq4">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4" aria-expanded="false" aria-controls="collapse4">
                                What is your commission structure?
                            </button>
                        </h2>
                        <div id="collapse4" class="accordion-collapse collapse" aria-labelledby="faq4" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Our commission structure is transparent and competitive. We provide detailed information about all costs upfront with no hidden charges. Contact us for specific details based on your requirements.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
