@extends('layouts.app')

@section('title', 'Schedule Property Visit - Luxury Real Estate')
@section('description', 'Schedule a visit to view luxury properties. Book your appointment for physical or virtual property tours at your convenience.')

@section('content')
<!-- Page Header -->
<section class="luxury-section bg-light" style="padding-top: 120px;">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title fade-in">
                    <p class="section-subtitle">Book Your Visit</p>
                    <h1 class="heading-font">Schedule Property Visit</h1>
                    <p class="lead">Book a personalized tour of our luxury properties at your convenience</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Schedule Visit Form -->
<section class="luxury-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card border-0 shadow-lg">
                    <div class="card-header bg-primary text-white text-center py-4" style="background: var(--primary-gold) !important;">
                        <h3 class="mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>Schedule Your Visit
                        </h3>
                        <p class="mb-0 mt-2">Choose your preferred date, time, and visit type</p>
                    </div>
                    <div class="card-body p-5">
                        @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        @endif

                        @if($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>Please correct the errors below.
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        @endif

                        <form action="{{ route('schedule-visit.store') }}" method="POST">
                            @csrf
                            
                            <!-- Personal Information -->
                            <div class="row">
                                <div class="col-12 mb-4">
                                    <h5 class="heading-font border-bottom pb-2">Personal Information</h5>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">Full Name *</label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name') }}" required>
                                    @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                           id="email" name="email" value="{{ old('email') }}" required>
                                    @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">Phone Number *</label>
                                    <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                                           id="phone" name="phone" value="{{ old('phone') }}" required>
                                    @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="property_id" class="form-label">Specific Property (Optional)</label>
                                    <select class="form-select" id="property_id" name="property_id">
                                        <option value="">Select a property or leave blank for general visit</option>
                                        <!-- Properties will be populated here -->
                                    </select>
                                </div>
                            </div>
                            
                            <!-- Visit Details -->
                            <div class="row">
                                <div class="col-12 mb-4 mt-4">
                                    <h5 class="heading-font border-bottom pb-2">Visit Details</h5>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="preferred_visit_date" class="form-label">Preferred Date *</label>
                                    <input type="date" class="form-control @error('preferred_visit_date') is-invalid @enderror" 
                                           id="preferred_visit_date" name="preferred_visit_date" 
                                           value="{{ old('preferred_visit_date') }}" 
                                           min="{{ date('Y-m-d', strtotime('+1 day')) }}" required>
                                    @error('preferred_visit_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="preferred_visit_time" class="form-label">Preferred Time *</label>
                                    <select class="form-select @error('preferred_visit_time') is-invalid @enderror" 
                                            id="preferred_visit_time" name="preferred_visit_time" required>
                                        <option value="">Select Time</option>
                                        <option value="09:00" {{ old('preferred_visit_time') == '09:00' ? 'selected' : '' }}>9:00 AM</option>
                                        <option value="10:00" {{ old('preferred_visit_time') == '10:00' ? 'selected' : '' }}>10:00 AM</option>
                                        <option value="11:00" {{ old('preferred_visit_time') == '11:00' ? 'selected' : '' }}>11:00 AM</option>
                                        <option value="12:00" {{ old('preferred_visit_time') == '12:00' ? 'selected' : '' }}>12:00 PM</option>
                                        <option value="14:00" {{ old('preferred_visit_time') == '14:00' ? 'selected' : '' }}>2:00 PM</option>
                                        <option value="15:00" {{ old('preferred_visit_time') == '15:00' ? 'selected' : '' }}>3:00 PM</option>
                                        <option value="16:00" {{ old('preferred_visit_time') == '16:00' ? 'selected' : '' }}>4:00 PM</option>
                                        <option value="17:00" {{ old('preferred_visit_time') == '17:00' ? 'selected' : '' }}>5:00 PM</option>
                                        <option value="18:00" {{ old('preferred_visit_time') == '18:00' ? 'selected' : '' }}>6:00 PM</option>
                                    </select>
                                    @error('preferred_visit_time')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <!-- Visit Type -->
                            <div class="mb-4">
                                <label class="form-label">Visit Type *</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card border-2 visit-type-card" data-type="physical">
                                            <div class="card-body text-center p-4">
                                                <i class="fas fa-walking fa-3x mb-3" style="color: var(--primary-gold);"></i>
                                                <h5 class="heading-font">Physical Visit</h5>
                                                <p class="text-muted small">In-person property tour with our expert</p>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="visit_type" id="physical" value="physical" {{ old('visit_type') == 'physical' ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="physical">
                                                        Select Physical Visit
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card border-2 visit-type-card" data-type="virtual">
                                            <div class="card-body text-center p-4">
                                                <i class="fas fa-video fa-3x mb-3" style="color: var(--primary-gold);"></i>
                                                <h5 class="heading-font">Virtual Tour</h5>
                                                <p class="text-muted small">Live video tour from comfort of your home</p>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="visit_type" id="virtual" value="virtual" {{ old('visit_type') == 'virtual' ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="virtual">
                                                        Select Virtual Tour
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Additional Information -->
                            <div class="row">
                                <div class="col-12 mb-4">
                                    <h5 class="heading-font border-bottom pb-2">Additional Information</h5>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="budget_range" class="form-label">Budget Range</label>
                                    <select class="form-select" id="budget_range" name="budget_range">
                                        <option value="">Select Budget</option>
                                        <option value="50L-1Cr" {{ old('budget_range') == '50L-1Cr' ? 'selected' : '' }}>₹50 Lakh - ₹1 Crore</option>
                                        <option value="1Cr-2Cr" {{ old('budget_range') == '1Cr-2Cr' ? 'selected' : '' }}>₹1 Crore - ₹2 Crore</option>
                                        <option value="2Cr-5Cr" {{ old('budget_range') == '2Cr-5Cr' ? 'selected' : '' }}>₹2 Crore - ₹5 Crore</option>
                                        <option value="5Cr-10Cr" {{ old('budget_range') == '5Cr-10Cr' ? 'selected' : '' }}>₹5 Crore - ₹10 Crore</option>
                                        <option value="10Cr+" {{ old('budget_range') == '10Cr+' ? 'selected' : '' }}>₹10 Crore+</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="property_type" class="form-label">Property Type Interest</label>
                                    <select class="form-select" id="property_type" name="property_type">
                                        <option value="">Any Type</option>
                                        <option value="apartment" {{ old('property_type') == 'apartment' ? 'selected' : '' }}>Apartment</option>
                                        <option value="villa" {{ old('property_type') == 'villa' ? 'selected' : '' }}>Villa</option>
                                        <option value="penthouse" {{ old('property_type') == 'penthouse' ? 'selected' : '' }}>Penthouse</option>
                                        <option value="townhouse" {{ old('property_type') == 'townhouse' ? 'selected' : '' }}>Townhouse</option>
                                        <option value="commercial" {{ old('property_type') == 'commercial' ? 'selected' : '' }}>Commercial</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="message" class="form-label">Special Requirements or Questions</label>
                                <textarea class="form-control" id="message" name="message" rows="4" 
                                          placeholder="Any specific requirements, questions, or areas of interest...">{{ old('message') }}</textarea>
                            </div>
                            
                            <div class="form-check mb-4">
                                <input class="form-check-input" type="checkbox" id="newsletter" 
                                       name="newsletter_subscription" value="1" {{ old('newsletter_subscription') ? 'checked' : '' }}>
                                <label class="form-check-label" for="newsletter">
                                    Subscribe to our newsletter for latest property updates and exclusive offers
                                </label>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-luxury-primary btn-lg">
                                    <i class="fas fa-calendar-check me-2"></i>Schedule My Visit
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Visit Benefits -->
<section class="luxury-section bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title fade-in">
                    <h2 class="heading-font">Why Schedule a Visit?</h2>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4 fade-in">
                <div class="text-center">
                    <i class="fas fa-eye fa-3x mb-3" style="color: var(--primary-gold);"></i>
                    <h5 class="heading-font">See Every Detail</h5>
                    <p class="text-muted">Experience the property's true ambiance, layout, and luxury features firsthand.</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4 fade-in">
                <div class="text-center">
                    <i class="fas fa-user-tie fa-3x mb-3" style="color: var(--primary-gold);"></i>
                    <h5 class="heading-font">Expert Guidance</h5>
                    <p class="text-muted">Get personalized insights from our luxury real estate specialists.</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4 fade-in">
                <div class="text-center">
                    <i class="fas fa-map-marked-alt fa-3x mb-3" style="color: var(--primary-gold);"></i>
                    <h5 class="heading-font">Location Analysis</h5>
                    <p class="text-muted">Understand the neighborhood, amenities, and investment potential.</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4 fade-in">
                <div class="text-center">
                    <i class="fas fa-handshake fa-3x mb-3" style="color: var(--primary-gold);"></i>
                    <h5 class="heading-font">Personalized Service</h5>
                    <p class="text-muted">Receive tailored recommendations based on your specific needs.</p>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
.visit-type-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.visit-type-card:hover {
    border-color: var(--primary-gold) !important;
    transform: translateY(-5px);
}

.visit-type-card.selected {
    border-color: var(--primary-gold) !important;
    background-color: rgba(212, 175, 55, 0.1);
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Visit type card selection
    const visitTypeCards = document.querySelectorAll('.visit-type-card');
    const radioButtons = document.querySelectorAll('input[name="visit_type"]');
    
    visitTypeCards.forEach(card => {
        card.addEventListener('click', function() {
            const type = this.dataset.type;
            const radio = document.getElementById(type);
            radio.checked = true;
            
            // Update card styles
            visitTypeCards.forEach(c => c.classList.remove('selected'));
            this.classList.add('selected');
        });
    });
    
    // Update card styles on radio change
    radioButtons.forEach(radio => {
        radio.addEventListener('change', function() {
            visitTypeCards.forEach(c => c.classList.remove('selected'));
            const selectedCard = document.querySelector(`[data-type="${this.value}"]`);
            if (selectedCard) {
                selectedCard.classList.add('selected');
            }
        });
    });
    
    // Set minimum date to tomorrow
    const dateInput = document.getElementById('preferred_visit_date');
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    dateInput.min = tomorrow.toISOString().split('T')[0];
});
</script>
@endpush
