@extends('layouts.app')

@section('title', 'Luxury Real Estate - Premium Properties & Investment Opportunities')
@section('description', 'Discover luxury real estate properties with world-class amenities. Premium apartments, villas, and commercial spaces with exceptional service and investment opportunities.')

@push('head')
<!-- Organization Schema -->
<script type="application/ld+json">
{!! App\Helpers\SchemaHelper::generateOrganizationSchema() !!}
</script>
@endpush

@section('content')
<!-- Enhanced Hero Section -->
<section class="hero-section-enhanced">
    <!-- Background Video/Image -->
    <div class="hero-background">
        <img src="{{ asset('images/hero-bg.jpg') }}" alt="Luxury Real Estate" class="hero-image">
        <div class="hero-overlay"></div>

        <!-- Floating Elements -->
        <div class="floating-elements">
            <div class="floating-element" data-speed="0.5">
                <i class="fas fa-home"></i>
            </div>
            <div class="floating-element" data-speed="0.8">
                <i class="fas fa-key"></i>
            </div>
            <div class="floating-element" data-speed="0.3">
                <i class="fas fa-building"></i>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-lg-10 text-center">
                <div class="hero-content-enhanced">
                    <!-- Animated Badge -->
                    <div class="hero-badge animate-fade-in-up" data-delay="0.2s">
                        <span class="badge-text">India's Premier Luxury Real Estate</span>
                    </div>

                    <!-- Main Title with Typewriter Effect -->
                    <h1 class="hero-title-enhanced heading-font animate-fade-in-up" data-delay="0.4s">
                        <span class="typewriter" data-text="Discover Luxury Living Beyond Imagination"></span>
                    </h1>

                    <!-- Subtitle -->
                    <p class="hero-subtitle-enhanced animate-fade-in-up" data-delay="0.6s">
                        Premium properties with world-class amenities, exceptional service, and unparalleled luxury experiences
                    </p>

                    <!-- Stats Counter -->
                    <div class="hero-stats animate-fade-in-up" data-delay="0.8s">
                        <div class="row justify-content-center">
                            <div class="col-md-3 col-6">
                                <div class="stat-item">
                                    <div class="stat-number" data-count="1000">0</div>
                                    <div class="stat-label">Properties Sold</div>
                                </div>
                            </div>
                            <div class="col-md-3 col-6">
                                <div class="stat-item">
                                    <div class="stat-number" data-count="500">0</div>
                                    <div class="stat-label">Happy Clients</div>
                                </div>
                            </div>
                            <div class="col-md-3 col-6">
                                <div class="stat-item">
                                    <div class="stat-number" data-count="15">0</div>
                                    <div class="stat-label">Years Experience</div>
                                </div>
                            </div>
                            <div class="col-md-3 col-6">
                                <div class="stat-item">
                                    <div class="stat-number" data-count="50">0</div>
                                    <div class="stat-label">Luxury Projects</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced CTA Buttons -->
                    <div class="hero-cta animate-fade-in-up" data-delay="1.0s">
                        <a href="{{ route('properties.index') }}" class="btn btn-luxury-primary-enhanced me-3">
                            <span class="btn-text">Explore Properties</span>
                            <span class="btn-icon"><i class="fas fa-arrow-right"></i></span>
                        </a>
                        <a href="{{ route('schedule-visit') }}" class="btn btn-luxury-secondary-enhanced">
                            <span class="btn-text">Schedule Visit</span>
                            <span class="btn-icon"><i class="fas fa-calendar"></i></span>
                        </a>
                    </div>

                    <!-- Scroll Indicator -->
                    <div class="scroll-indicator animate-fade-in-up" data-delay="1.2s">
                        <div class="scroll-text">Scroll to Explore</div>
                        <div class="scroll-arrow">
                            <i class="fas fa-chevron-down"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Property Search Bar -->
<section class="search-bar-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="search-bar-card">
                    <form action="{{ route('properties.index') }}" method="GET" class="property-search-form">
                        <div class="row g-3 align-items-end">
                            <div class="col-lg-3 col-md-6">
                                <label class="form-label">Property Type</label>
                                <select name="type" class="form-select">
                                    <option value="">All Types</option>
                                    <option value="apartment">Apartment</option>
                                    <option value="villa">Villa</option>
                                    <option value="penthouse">Penthouse</option>
                                    <option value="commercial">Commercial</option>
                                </select>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <label class="form-label">Location</label>
                                <select name="city" class="form-select">
                                    <option value="">All Cities</option>
                                    <option value="Mumbai">Mumbai</option>
                                    <option value="Delhi">Delhi</option>
                                    <option value="Bangalore">Bangalore</option>
                                    <option value="Pune">Pune</option>
                                </select>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <label class="form-label">Budget Range</label>
                                <select name="budget" class="form-select">
                                    <option value="">Any Budget</option>
                                    <option value="50L-1Cr">₹50L - ₹1Cr</option>
                                    <option value="1Cr-2Cr">₹1Cr - ₹2Cr</option>
                                    <option value="2Cr-5Cr">₹2Cr - ₹5Cr</option>
                                    <option value="5Cr+">₹5Cr+</option>
                                </select>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <button type="submit" class="btn btn-luxury-primary w-100">
                                    <i class="fas fa-search me-2"></i>Search Properties
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Properties Section -->
<section class="luxury-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title fade-in">
                    <p class="section-subtitle">Premium Collection</p>
                    <h2 class="heading-font">Featured Properties</h2>
                    <p class="lead">Handpicked luxury properties with exceptional amenities and prime locations</p>
                </div>
            </div>
        </div>
        
        <div class="row">
            @forelse($featuredProperties as $property)
            <div class="col-lg-4 col-md-6 mb-4 fade-in">
                <div class="property-card">
                    <div class="property-image">
                        <img src="{{ $property->featured_image_url }}" alt="{{ $property->title }}" class="img-fluid">
                        <div class="property-price">{{ $property->formatted_price }}</div>
                        @if($property->is_luxury)
                        <div class="position-absolute top-0 start-0 m-3">
                            <span class="badge" style="background: var(--primary-gold);">Luxury</span>
                        </div>
                        @endif
                    </div>
                    <div class="property-content">
                        <h4 class="property-title">{{ $property->title }}</h4>
                        <p class="property-location">
                            <i class="fas fa-map-marker-alt me-2" style="color: var(--primary-gold);"></i>
                            {{ $property->city }}, {{ $property->state }}
                        </p>
                        <div class="property-features">
                            @if($property->bedrooms)
                            <div class="feature-item">
                                <i class="fas fa-bed"></i>
                                <span>{{ $property->bedrooms }} Beds</span>
                            </div>
                            @endif
                            @if($property->bathrooms)
                            <div class="feature-item">
                                <i class="fas fa-bath"></i>
                                <span>{{ $property->bathrooms }} Baths</span>
                            </div>
                            @endif
                            @if($property->area_sqft)
                            <div class="feature-item">
                                <i class="fas fa-ruler-combined"></i>
                                <span>{{ $property->formatted_area }}</span>
                            </div>
                            @endif
                        </div>
                        <a href="{{ route('properties.show', $property) }}" class="btn btn-luxury-primary w-100">View Details</a>
                    </div>
                </div>
            </div>
            @empty
            <div class="col-12 text-center">
                <p class="lead">No featured properties available at the moment.</p>
            </div>
            @endforelse
        </div>
        
        <div class="row">
            <div class="col-12 text-center mt-4">
                <a href="{{ route('properties.index') }}" class="btn btn-luxury-secondary">View All Properties</a>
            </div>
        </div>
    </div>
</section>

<!-- Luxury Promise Section -->
<section class="luxury-section bg-light parallax-section" style="background-image: url('{{ asset('images/luxury-bg.jpg') }}');">
    <div class="parallax-overlay"></div>
    <div class="container position-relative">
        <div class="row align-items-center">
            <div class="col-lg-6 fade-in">
                <div class="pe-lg-5">
                    <p class="section-subtitle text-white">Our Promise</p>
                    <h2 class="heading-font text-white mb-4">Luxury Redefined</h2>
                    <p class="text-white mb-4">We don't just sell properties; we curate lifestyle experiences. Every property in our portfolio is carefully selected to meet the highest standards of luxury, comfort, and investment potential.</p>
                    
                    <div class="row">
                        <div class="col-6 mb-3">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="fas fa-award fa-2x" style="color: var(--primary-gold);"></i>
                                </div>
                                <div>
                                    <h5 class="text-white mb-1">Premium Quality</h5>
                                    <p class="text-white-50 mb-0 small">World-class amenities</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="fas fa-map-marked-alt fa-2x" style="color: var(--primary-gold);"></i>
                                </div>
                                <div>
                                    <h5 class="text-white mb-1">Prime Locations</h5>
                                    <p class="text-white-50 mb-0 small">Strategic positioning</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="fas fa-handshake fa-2x" style="color: var(--primary-gold);"></i>
                                </div>
                                <div>
                                    <h5 class="text-white mb-1">Expert Service</h5>
                                    <p class="text-white-50 mb-0 small">Personalized assistance</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="fas fa-chart-line fa-2x" style="color: var(--primary-gold);"></i>
                                </div>
                                <div>
                                    <h5 class="text-white mb-1">Investment Value</h5>
                                    <p class="text-white-50 mb-0 small">High ROI potential</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <a href="{{ route('about') }}" class="btn btn-luxury-primary">Learn More</a>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 fade-in">
                <div class="ps-lg-5">
                    <img src="{{ asset('images/luxury-interior.jpg') }}" alt="Luxury Interior" class="img-fluid rounded shadow-lg">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Luxury Properties Showcase -->
@if($luxuryProperties->count() > 0)
<section class="luxury-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title fade-in">
                    <p class="section-subtitle">Ultra Premium</p>
                    <h2 class="heading-font">Luxury Collection</h2>
                    <p class="lead">Exclusive properties for the most discerning clients</p>
                </div>
            </div>
        </div>
        
        <div class="row">
            @foreach($luxuryProperties as $property)
            <div class="col-lg-4 col-md-6 mb-4 fade-in">
                <div class="property-card">
                    <div class="property-image">
                        <img src="{{ $property->featured_image_url }}" alt="{{ $property->title }}" class="img-fluid">
                        <div class="property-price">{{ $property->formatted_price }}</div>
                        <div class="position-absolute top-0 start-0 m-3">
                            <span class="badge" style="background: var(--primary-gold);">Ultra Luxury</span>
                        </div>
                    </div>
                    <div class="property-content">
                        <h4 class="property-title">{{ $property->title }}</h4>
                        <p class="property-location">
                            <i class="fas fa-map-marker-alt me-2" style="color: var(--primary-gold);"></i>
                            {{ $property->city }}, {{ $property->state }}
                        </p>
                        <p class="text-muted small">{{ $property->short_description }}</p>
                        <a href="{{ route('properties.show', $property) }}" class="btn btn-luxury-primary w-100">Explore Luxury</a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Testimonials Section -->
@if($testimonials->count() > 0)
<section class="luxury-section bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title fade-in">
                    <p class="section-subtitle">Client Stories</p>
                    <h2 class="heading-font">What Our Clients Say</h2>
                    <p class="lead">Testimonials from satisfied luxury property owners</p>
                </div>
            </div>
        </div>
        
        <div class="row">
            @foreach($testimonials as $testimonial)
            <div class="col-lg-4 col-md-6 mb-4 fade-in">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body p-4">
                        <div class="mb-3">
                            @for($i = 1; $i <= 5; $i++)
                                <i class="fas fa-star {{ $i <= $testimonial->rating ? 'text-warning' : 'text-muted' }}"></i>
                            @endfor
                        </div>
                        <p class="card-text">"{{ $testimonial->testimonial }}"</p>
                        <div class="d-flex align-items-center mt-3">
                            @if($testimonial->client_image)
                            <img src="{{ asset('storage/' . $testimonial->client_image) }}" alt="{{ $testimonial->client_name }}" class="rounded-circle me-3" width="50" height="50">
                            @else
                            <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                <span class="text-white fw-bold">{{ substr($testimonial->client_name, 0, 1) }}</span>
                            </div>
                            @endif
                            <div>
                                <h6 class="mb-0">{{ $testimonial->client_name }}</h6>
                                @if($testimonial->client_designation)
                                <small class="text-muted">{{ $testimonial->client_designation }}</small>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        
        <div class="row">
            <div class="col-12 text-center mt-4">
                <a href="{{ route('testimonials') }}" class="btn btn-luxury-secondary">View All Testimonials</a>
            </div>
        </div>
    </div>
</section>
@endif

<!-- Call to Action Section -->
<section class="luxury-section parallax-section" style="background-image: url('{{ asset('images/cta-bg.jpg') }}');">
    <div class="parallax-overlay"></div>
    <div class="container position-relative">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center fade-in">
                <h2 class="heading-font text-white mb-4">Ready to Find Your Dream Home?</h2>
                <p class="text-white mb-4 lead">Let our luxury real estate experts help you discover the perfect property that matches your lifestyle and investment goals.</p>
                <div>
                    <a href="{{ route('contact') }}" class="btn btn-luxury-primary me-3">Get In Touch</a>
                    <a href="{{ route('schedule-visit') }}" class="btn btn-luxury-secondary">Schedule a Visit</a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced animations and interactions
    initializeHomepageAnimations();
    initializeCounterAnimations();
    initializeTypewriterEffect();
    initializeParallaxEffects();
    initializeSearchForm();

    // Fade in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);

    document.querySelectorAll('.fade-in').forEach(el => {
        observer.observe(el);
    });
});

// Initialize homepage animations
function initializeHomepageAnimations() {
    // Smooth scroll for scroll indicator
    const scrollIndicator = document.querySelector('.scroll-indicator');
    if (scrollIndicator) {
        scrollIndicator.addEventListener('click', function() {
            const nextSection = document.querySelector('.search-bar-section');
            if (nextSection) {
                nextSection.scrollIntoView({ behavior: 'smooth' });
            }
        });
    }

    // Parallax effect for floating elements
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.floating-element');

        parallaxElements.forEach(function(element) {
            const speed = element.dataset.speed || 0.5;
            const yPos = -(scrolled * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });
    });
}

// Counter animation
function initializeCounterAnimations() {
    const counters = document.querySelectorAll('.stat-number');
    const counterObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                const target = parseInt(counter.dataset.count);
                const duration = 2000; // 2 seconds
                const increment = target / (duration / 16); // 60fps
                let current = 0;

                const updateCounter = () => {
                    current += increment;
                    if (current < target) {
                        counter.textContent = Math.floor(current);
                        requestAnimationFrame(updateCounter);
                    } else {
                        counter.textContent = target;
                    }
                };

                updateCounter();
                counterObserver.unobserve(counter);
            }
        });
    }, { threshold: 0.5 });

    counters.forEach(counter => counterObserver.observe(counter));
}

// Typewriter effect
function initializeTypewriterEffect() {
    const typewriterElement = document.querySelector('.typewriter');
    if (!typewriterElement) return;

    const text = typewriterElement.dataset.text;
    const speed = 100; // milliseconds per character
    let i = 0;

    typewriterElement.textContent = '';

    function typeWriter() {
        if (i < text.length) {
            typewriterElement.textContent += text.charAt(i);
            i++;
            setTimeout(typeWriter, speed);
        } else {
            // Remove cursor after typing is complete
            setTimeout(() => {
                typewriterElement.style.borderRight = 'none';
            }, 1000);
        }
    }

    // Start typewriter effect after a delay
    setTimeout(typeWriter, 1000);
}

// Parallax effects
function initializeParallaxEffects() {
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const heroVideo = document.querySelector('.hero-video');

        if (heroVideo) {
            const speed = 0.5;
            heroVideo.style.transform = `translateY(${scrolled * speed}px)`;
        }
    });
}

// Enhanced search form
function initializeSearchForm() {
    const searchForm = document.querySelector('.property-search-form');
    if (!searchForm) return;

    // Add loading state to search button
    searchForm.addEventListener('submit', function() {
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;

        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Searching...';
        submitBtn.disabled = true;

        // Re-enable after a delay (in case of client-side validation errors)
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 3000);
    });

    // Enhanced form interactions
    const formSelects = searchForm.querySelectorAll('.form-select');
    formSelects.forEach(select => {
        select.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        select.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });

        select.addEventListener('change', function() {
            if (this.value) {
                this.classList.add('has-value');
            } else {
                this.classList.remove('has-value');
            }
        });
    });
}

// Property card hover effects
document.addEventListener('DOMContentLoaded', function() {
    const propertyCards = document.querySelectorAll('.property-card');

    propertyCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});

// Enhanced button interactions
document.addEventListener('DOMContentLoaded', function() {
    const enhancedButtons = document.querySelectorAll('.btn-luxury-primary-enhanced, .btn-luxury-secondary-enhanced');

    enhancedButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Create ripple effect
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
});
</script>

<!-- Add ripple effect CSS -->
<style>
.btn-luxury-primary-enhanced,
.btn-luxury-secondary-enhanced {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

.focused {
    transform: scale(1.02);
    transition: transform 0.3s ease;
}

.form-select.has-value {
    border-color: var(--primary-gold);
    background-color: rgba(212, 175, 55, 0.05);
}

/* Mobile responsiveness for enhanced hero */
@media (max-width: 768px) {
    .hero-title-enhanced {
        font-size: 2.5rem;
    }

    .hero-subtitle-enhanced {
        font-size: 1.1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .btn-luxury-primary-enhanced,
    .btn-luxury-secondary-enhanced {
        padding: 12px 25px;
        font-size: 1rem;
    }

    .search-bar-section {
        margin-top: -40px;
    }

    .search-bar-card {
        padding: 1.5rem;
    }
}

@media (max-width: 576px) {
    .hero-title-enhanced {
        font-size: 2rem;
    }

    .hero-stats .col-6 {
        margin-bottom: 1rem;
    }
}
</style>
@endpush
