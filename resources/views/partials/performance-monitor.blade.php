<!-- Performance Monitoring -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Core Web Vitals monitoring
    function measureWebVitals() {
        // Largest Contentful Paint (LCP)
        new PerformanceObserver((entryList) => {
            const entries = entryList.getEntries();
            const lastEntry = entries[entries.length - 1];
            
            if (typeof gtag !== 'undefined') {
                gtag('event', 'web_vitals', {
                    'metric_name': 'LCP',
                    'metric_value': Math.round(lastEntry.startTime),
                    'metric_rating': lastEntry.startTime < 2500 ? 'good' : lastEntry.startTime < 4000 ? 'needs_improvement' : 'poor'
                });
            }
        }).observe({entryTypes: ['largest-contentful-paint']});

        // First Input Delay (FID)
        new PerformanceObserver((entryList) => {
            const entries = entryList.getEntries();
            entries.forEach((entry) => {
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'web_vitals', {
                        'metric_name': 'FID',
                        'metric_value': Math.round(entry.processingStart - entry.startTime),
                        'metric_rating': entry.processingStart - entry.startTime < 100 ? 'good' : entry.processingStart - entry.startTime < 300 ? 'needs_improvement' : 'poor'
                    });
                }
            });
        }).observe({entryTypes: ['first-input']});

        // Cumulative Layout Shift (CLS)
        let clsValue = 0;
        let clsEntries = [];
        
        new PerformanceObserver((entryList) => {
            const entries = entryList.getEntries();
            entries.forEach((entry) => {
                if (!entry.hadRecentInput) {
                    clsValue += entry.value;
                    clsEntries.push(entry);
                }
            });
            
            if (typeof gtag !== 'undefined') {
                gtag('event', 'web_vitals', {
                    'metric_name': 'CLS',
                    'metric_value': Math.round(clsValue * 1000) / 1000,
                    'metric_rating': clsValue < 0.1 ? 'good' : clsValue < 0.25 ? 'needs_improvement' : 'poor'
                });
            }
        }).observe({entryTypes: ['layout-shift']});
    }

    // Page load performance
    function measurePagePerformance() {
        window.addEventListener('load', function() {
            setTimeout(function() {
                const perfData = performance.getEntriesByType('navigation')[0];
                
                if (perfData && typeof gtag !== 'undefined') {
                    gtag('event', 'page_performance', {
                        'dns_time': Math.round(perfData.domainLookupEnd - perfData.domainLookupStart),
                        'connect_time': Math.round(perfData.connectEnd - perfData.connectStart),
                        'response_time': Math.round(perfData.responseEnd - perfData.requestStart),
                        'dom_load_time': Math.round(perfData.domContentLoadedEventEnd - perfData.navigationStart),
                        'page_load_time': Math.round(perfData.loadEventEnd - perfData.navigationStart)
                    });
                }
            }, 0);
        });
    }

    // Resource loading performance
    function measureResourcePerformance() {
        window.addEventListener('load', function() {
            setTimeout(function() {
                const resources = performance.getEntriesByType('resource');
                const slowResources = resources.filter(resource => resource.duration > 1000);
                
                slowResources.forEach(function(resource) {
                    if (typeof gtag !== 'undefined') {
                        gtag('event', 'slow_resource', {
                            'resource_name': resource.name,
                            'resource_type': resource.initiatorType,
                            'load_time': Math.round(resource.duration),
                            'resource_size': resource.transferSize || 0
                        });
                    }
                });
            }, 1000);
        });
    }

    // Error tracking
    function trackErrors() {
        window.addEventListener('error', function(e) {
            if (typeof gtag !== 'undefined') {
                gtag('event', 'javascript_error', {
                    'error_message': e.message,
                    'error_filename': e.filename,
                    'error_line': e.lineno,
                    'error_column': e.colno,
                    'page_location': window.location.href
                });
            }
        });

        window.addEventListener('unhandledrejection', function(e) {
            if (typeof gtag !== 'undefined') {
                gtag('event', 'promise_rejection', {
                    'error_message': e.reason.toString(),
                    'page_location': window.location.href
                });
            }
        });
    }

    // Network connection monitoring
    function monitorConnection() {
        if ('connection' in navigator) {
            const connection = navigator.connection;
            
            if (typeof gtag !== 'undefined') {
                gtag('event', 'connection_info', {
                    'connection_type': connection.effectiveType,
                    'connection_downlink': connection.downlink,
                    'connection_rtt': connection.rtt,
                    'save_data': connection.saveData
                });
            }

            connection.addEventListener('change', function() {
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'connection_change', {
                        'new_connection_type': connection.effectiveType,
                        'new_downlink': connection.downlink,
                        'new_rtt': connection.rtt
                    });
                }
            });
        }
    }

    // Memory usage monitoring (if available)
    function monitorMemory() {
        if ('memory' in performance) {
            const memory = performance.memory;
            
            if (typeof gtag !== 'undefined') {
                gtag('event', 'memory_usage', {
                    'used_heap': Math.round(memory.usedJSHeapSize / 1048576), // MB
                    'total_heap': Math.round(memory.totalJSHeapSize / 1048576), // MB
                    'heap_limit': Math.round(memory.jsHeapSizeLimit / 1048576) // MB
                });
            }
        }
    }

    // Device and browser information
    function trackDeviceInfo() {
        if (typeof gtag !== 'undefined') {
            gtag('event', 'device_info', {
                'screen_resolution': screen.width + 'x' + screen.height,
                'viewport_size': window.innerWidth + 'x' + window.innerHeight,
                'device_pixel_ratio': window.devicePixelRatio,
                'color_depth': screen.colorDepth,
                'timezone': Intl.DateTimeFormat().resolvedOptions().timeZone,
                'language': navigator.language,
                'platform': navigator.platform,
                'user_agent': navigator.userAgent.substring(0, 100) // Truncate for privacy
            });
        }
    }

    // Initialize all monitoring
    if (typeof PerformanceObserver !== 'undefined') {
        measureWebVitals();
    }
    
    measurePagePerformance();
    measureResourcePerformance();
    trackErrors();
    monitorConnection();
    
    // Delay memory and device tracking to avoid impacting performance
    setTimeout(function() {
        monitorMemory();
        trackDeviceInfo();
    }, 2000);

    // Track page visibility changes
    document.addEventListener('visibilitychange', function() {
        if (typeof gtag !== 'undefined') {
            gtag('event', 'page_visibility', {
                'visibility_state': document.visibilityState,
                'page_location': window.location.href
            });
        }
    });

    // Track page unload
    window.addEventListener('beforeunload', function() {
        const timeOnPage = Math.round((Date.now() - performance.timing.navigationStart) / 1000);
        
        if (typeof gtag !== 'undefined') {
            gtag('event', 'page_unload', {
                'time_on_page': timeOnPage,
                'page_location': window.location.href
            });
        }
    });
});

// Service Worker registration for PWA capabilities
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
                console.log('ServiceWorker registration successful');
                
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'service_worker', {
                        'event_action': 'registered',
                        'page_location': window.location.href
                    });
                }
            })
            .catch(function(error) {
                console.log('ServiceWorker registration failed');
                
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'service_worker', {
                        'event_action': 'registration_failed',
                        'error_message': error.toString(),
                        'page_location': window.location.href
                    });
                }
            });
    });
}
</script>
