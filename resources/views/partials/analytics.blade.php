<!-- Google Analytics 4 -->
@if(config('services.google_analytics.tracking_id'))
<script async src="https://www.googletagmanager.com/gtag/js?id={{ config('services.google_analytics.tracking_id') }}"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', '{{ config('services.google_analytics.tracking_id') }}', {
    page_title: '{{ $title ?? 'LuxuryEstate' }}',
    page_location: '{{ url()->current() }}',
    custom_map: {
      'custom_parameter_1': 'property_type',
      'custom_parameter_2': 'property_price_range'
    }
  });

  // Enhanced E-commerce tracking for property views
  @if(isset($property))
  gtag('event', 'view_item', {
    currency: 'INR',
    value: {{ $property->price }},
    items: [{
      item_id: '{{ $property->id }}',
      item_name: '{{ $property->title }}',
      item_category: '{{ $property->type }}',
      item_category2: '{{ $property->city }}',
      item_category3: '{{ $property->is_luxury ? "luxury" : "standard" }}',
      price: {{ $property->price }},
      quantity: 1
    }]
  });
  @endif

  // Track property inquiries
  function trackPropertyInquiry(propertyId, propertyTitle, inquiryType) {
    gtag('event', 'generate_lead', {
      currency: 'INR',
      value: 0,
      items: [{
        item_id: propertyId,
        item_name: propertyTitle,
        item_category: inquiryType
      }]
    });
  }

  // Track virtual tour views
  function trackVirtualTour(propertyId, propertyTitle) {
    gtag('event', 'view_promotion', {
      creative_name: 'Virtual Tour',
      creative_slot: 'property_detail',
      promotion_id: propertyId,
      promotion_name: propertyTitle
    });
  }

  // Track brochure downloads
  function trackBrochureDownload(propertyId, propertyTitle) {
    gtag('event', 'select_content', {
      content_type: 'brochure',
      content_id: propertyId,
      custom_parameter_1: propertyTitle
    });
  }
</script>
@endif

<!-- Facebook Pixel -->
@if(config('services.facebook_pixel.pixel_id'))
<script>
!function(f,b,e,v,n,t,s)
{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};
if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];
s.parentNode.insertBefore(t,s)}(window, document,'script',
'https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '{{ config('services.facebook_pixel.pixel_id') }}');
fbq('track', 'PageView');

@if(isset($property))
// Track property view
fbq('track', 'ViewContent', {
  content_type: 'property',
  content_ids: ['{{ $property->id }}'],
  content_name: '{{ $property->title }}',
  content_category: '{{ $property->type }}',
  value: {{ $property->price }},
  currency: 'INR'
});
@endif

// Track lead generation
function trackFacebookLead(propertyId, propertyTitle, leadValue) {
  fbq('track', 'Lead', {
    content_name: propertyTitle,
    content_category: 'property_inquiry',
    content_ids: [propertyId],
    value: leadValue || 0,
    currency: 'INR'
  });
}
</script>
<noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id={{ config('services.facebook_pixel.pixel_id') }}&ev=PageView&noscript=1"/></noscript>
@endif

<!-- Hotjar Tracking -->
@if(config('services.hotjar.site_id'))
<script>
    (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:{{ config('services.hotjar.site_id') }},hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
</script>
@endif

<!-- Custom Analytics for Property Interactions -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Track property card clicks
    document.querySelectorAll('.property-card').forEach(function(card) {
        card.addEventListener('click', function() {
            const propertyTitle = this.querySelector('.property-title')?.textContent || 'Unknown Property';
            const propertyPrice = this.querySelector('.property-price')?.textContent || '0';
            
            // Send custom event
            if (typeof gtag !== 'undefined') {
                gtag('event', 'property_card_click', {
                    'property_name': propertyTitle,
                    'property_price': propertyPrice,
                    'page_location': window.location.href
                });
            }
        });
    });

    // Track contact form submissions
    document.querySelectorAll('form[action*="contact"]').forEach(function(form) {
        form.addEventListener('submit', function() {
            const formType = this.querySelector('input[name="type"]')?.value || 'general';
            const propertyId = this.querySelector('input[name="property_id"]')?.value;
            
            if (typeof gtag !== 'undefined') {
                gtag('event', 'form_submit', {
                    'form_type': formType,
                    'property_id': propertyId || 'none',
                    'page_location': window.location.href
                });
            }

            // Track Facebook lead if property inquiry
            if (propertyId && typeof trackFacebookLead !== 'undefined') {
                trackFacebookLead(propertyId, 'Property Inquiry', 0);
            }
        });
    });

    // Track WhatsApp clicks
    document.querySelectorAll('a[href*="wa.me"]').forEach(function(link) {
        link.addEventListener('click', function() {
            if (typeof gtag !== 'undefined') {
                gtag('event', 'whatsapp_click', {
                    'page_location': window.location.href,
                    'link_url': this.href
                });
            }
        });
    });

    // Track phone number clicks
    document.querySelectorAll('a[href^="tel:"]').forEach(function(link) {
        link.addEventListener('click', function() {
            if (typeof gtag !== 'undefined') {
                gtag('event', 'phone_click', {
                    'page_location': window.location.href,
                    'phone_number': this.href.replace('tel:', '')
                });
            }
        });
    });

    // Track scroll depth
    let maxScroll = 0;
    let scrollTracked = {25: false, 50: false, 75: false, 100: false};
    
    window.addEventListener('scroll', function() {
        const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
        maxScroll = Math.max(maxScroll, scrollPercent);
        
        [25, 50, 75, 100].forEach(function(threshold) {
            if (maxScroll >= threshold && !scrollTracked[threshold]) {
                scrollTracked[threshold] = true;
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'scroll', {
                        'scroll_depth': threshold,
                        'page_location': window.location.href
                    });
                }
            }
        });
    });

    // Track time on page
    let startTime = Date.now();
    let timeTracked = {30: false, 60: false, 120: false, 300: false};
    
    setInterval(function() {
        const timeOnPage = Math.round((Date.now() - startTime) / 1000);
        
        [30, 60, 120, 300].forEach(function(threshold) {
            if (timeOnPage >= threshold && !timeTracked[threshold]) {
                timeTracked[threshold] = true;
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'timing_complete', {
                        'name': 'time_on_page',
                        'value': threshold,
                        'page_location': window.location.href
                    });
                }
            }
        });
    }, 5000);
});

// Global functions for tracking specific events
window.trackPropertyInquiry = function(propertyId, propertyTitle, inquiryType) {
    if (typeof gtag !== 'undefined') {
        trackPropertyInquiry(propertyId, propertyTitle, inquiryType);
    }
    if (typeof trackFacebookLead !== 'undefined') {
        trackFacebookLead(propertyId, propertyTitle, 0);
    }
};

window.trackVirtualTourView = function(propertyId, propertyTitle) {
    if (typeof gtag !== 'undefined') {
        trackVirtualTour(propertyId, propertyTitle);
    }
};

window.trackBrochureDownload = function(propertyId, propertyTitle) {
    if (typeof gtag !== 'undefined') {
        trackBrochureDownload(propertyId, propertyTitle);
    }
};
</script>
