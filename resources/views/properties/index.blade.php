@extends('layouts.app')

@section('title', 'Luxury Properties - Premium Real Estate Collection')
@section('description', 'Browse our exclusive collection of luxury properties including penthouses, villas, and premium apartments with world-class amenities.')

@section('content')
<!-- Page Header -->
<section class="luxury-section bg-light" style="padding-top: 120px;">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title fade-in">
                    <p class="section-subtitle">Premium Collection</p>
                    <h1 class="heading-font">Luxury Properties</h1>
                    <p class="lead">Discover exceptional properties with world-class amenities and prime locations</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Filters Section -->
<section class="py-4 bg-white border-bottom">
    <div class="container">
        <form method="GET" action="{{ route('properties.index') }}" class="row g-3 align-items-end">
            <div class="col-md-3">
                <label for="type" class="form-label fw-semibold">Property Type</label>
                <select name="type" id="type" class="form-select">
                    <option value="">All Types</option>
                    <option value="apartment" {{ request('type') == 'apartment' ? 'selected' : '' }}>Apartment</option>
                    <option value="villa" {{ request('type') == 'villa' ? 'selected' : '' }}>Villa</option>
                    <option value="penthouse" {{ request('type') == 'penthouse' ? 'selected' : '' }}>Penthouse</option>
                    <option value="townhouse" {{ request('type') == 'townhouse' ? 'selected' : '' }}>Townhouse</option>
                    <option value="commercial" {{ request('type') == 'commercial' ? 'selected' : '' }}>Commercial</option>
                </select>
            </div>
            
            <div class="col-md-2">
                <label for="city" class="form-label fw-semibold">City</label>
                <select name="city" id="city" class="form-select">
                    <option value="">All Cities</option>
                    <option value="Mumbai" {{ request('city') == 'Mumbai' ? 'selected' : '' }}>Mumbai</option>
                    <option value="Delhi" {{ request('city') == 'Delhi' ? 'selected' : '' }}>Delhi</option>
                    <option value="Bangalore" {{ request('city') == 'Bangalore' ? 'selected' : '' }}>Bangalore</option>
                    <option value="Pune" {{ request('city') == 'Pune' ? 'selected' : '' }}>Pune</option>
                </select>
            </div>
            
            <div class="col-md-2">
                <label for="min_price" class="form-label fw-semibold">Min Price (₹)</label>
                <select name="min_price" id="min_price" class="form-select">
                    <option value="">No Min</option>
                    <option value="5000000" {{ request('min_price') == '5000000' ? 'selected' : '' }}>50 Lakh</option>
                    <option value="10000000" {{ request('min_price') == '10000000' ? 'selected' : '' }}>1 Crore</option>
                    <option value="20000000" {{ request('min_price') == '20000000' ? 'selected' : '' }}>2 Crore</option>
                    <option value="50000000" {{ request('min_price') == '50000000' ? 'selected' : '' }}>5 Crore</option>
                </select>
            </div>
            
            <div class="col-md-2">
                <label for="max_price" class="form-label fw-semibold">Max Price (₹)</label>
                <select name="max_price" id="max_price" class="form-select">
                    <option value="">No Max</option>
                    <option value="10000000" {{ request('max_price') == '10000000' ? 'selected' : '' }}>1 Crore</option>
                    <option value="20000000" {{ request('max_price') == '20000000' ? 'selected' : '' }}>2 Crore</option>
                    <option value="50000000" {{ request('max_price') == '50000000' ? 'selected' : '' }}>5 Crore</option>
                    <option value="100000000" {{ request('max_price') == '100000000' ? 'selected' : '' }}>10 Crore</option>
                </select>
            </div>
            
            <div class="col-md-2">
                <label for="bedrooms" class="form-label fw-semibold">Bedrooms</label>
                <select name="bedrooms" id="bedrooms" class="form-select">
                    <option value="">Any</option>
                    <option value="1" {{ request('bedrooms') == '1' ? 'selected' : '' }}>1 BHK</option>
                    <option value="2" {{ request('bedrooms') == '2' ? 'selected' : '' }}>2 BHK</option>
                    <option value="3" {{ request('bedrooms') == '3' ? 'selected' : '' }}>3 BHK</option>
                    <option value="4" {{ request('bedrooms') == '4' ? 'selected' : '' }}>4+ BHK</option>
                </select>
            </div>
            
            <div class="col-md-1">
                <button type="submit" class="btn btn-luxury-primary w-100">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
    </div>
</section>

<!-- Properties Grid -->
<section class="luxury-section">
    <div class="container">
        <!-- Results Info -->
        <div class="row mb-4">
            <div class="col-md-6">
                <p class="mb-0">
                    <strong>{{ $properties->total() }}</strong> properties found
                    @if(request()->hasAny(['type', 'city', 'min_price', 'max_price', 'bedrooms']))
                        <span class="text-muted">with your filters</span>
                    @endif
                </p>
            </div>
            <div class="col-md-6 text-md-end">
                <div class="d-flex align-items-center justify-content-md-end">
                    <label for="sort" class="form-label me-2 mb-0">Sort by:</label>
                    <select name="sort" id="sort" class="form-select w-auto" onchange="this.form.submit()">
                        <option value="newest">Newest First</option>
                        <option value="price_low">Price: Low to High</option>
                        <option value="price_high">Price: High to Low</option>
                        <option value="featured">Featured First</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Properties Grid -->
        <div class="row">
            @forelse($properties as $property)
            <div class="col-lg-4 col-md-6 mb-4 fade-in">
                <div class="property-card">
                    <div class="property-image">
                        <img src="{{ $property->featured_image_url }}" alt="{{ $property->title }}" class="img-fluid">
                        <div class="property-price">{{ $property->formatted_price }}</div>
                        
                        <!-- Property Badges -->
                        <div class="position-absolute top-0 start-0 m-3">
                            @if($property->is_featured)
                            <span class="badge bg-warning text-dark me-1">Featured</span>
                            @endif
                            @if($property->is_luxury)
                            <span class="badge" style="background: var(--primary-gold);">Luxury</span>
                            @endif
                        </div>
                        
                        <!-- Property Status -->
                        <div class="position-absolute top-0 end-0 m-3">
                            <span class="badge bg-success">{{ ucfirst($property->status) }}</span>
                        </div>
                        
                        <!-- Quick Actions -->
                        <div class="position-absolute bottom-0 end-0 m-3">
                            <div class="btn-group-vertical">
                                @if($property->virtual_tour_url)
                                <a href="{{ $property->virtual_tour_url }}" target="_blank" class="btn btn-sm btn-light" data-bs-toggle="tooltip" title="Virtual Tour">
                                    <i class="fas fa-vr-cardboard"></i>
                                </a>
                                @endif
                                <button class="btn btn-sm btn-light" data-bs-toggle="tooltip" title="Add to Wishlist">
                                    <i class="far fa-heart"></i>
                                </button>
                                <button class="btn btn-sm btn-light" data-bs-toggle="tooltip" title="Share">
                                    <i class="fas fa-share-alt"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="property-content">
                        <h4 class="property-title">
                            <a href="{{ route('properties.show', $property) }}" class="text-decoration-none text-dark">
                                {{ $property->title }}
                            </a>
                        </h4>
                        
                        <p class="property-location">
                            <i class="fas fa-map-marker-alt me-2" style="color: var(--primary-gold);"></i>
                            {{ $property->city }}, {{ $property->state }}
                        </p>
                        
                        <p class="text-muted small mb-3">{{ $property->short_description }}</p>
                        
                        <div class="property-features mb-3">
                            @if($property->bedrooms)
                            <div class="feature-item">
                                <i class="fas fa-bed"></i>
                                <span>{{ $property->bedrooms }} Beds</span>
                            </div>
                            @endif
                            @if($property->bathrooms)
                            <div class="feature-item">
                                <i class="fas fa-bath"></i>
                                <span>{{ $property->bathrooms }} Baths</span>
                            </div>
                            @endif
                            @if($property->area_sqft)
                            <div class="feature-item">
                                <i class="fas fa-ruler-combined"></i>
                                <span>{{ $property->formatted_area }}</span>
                            </div>
                            @endif
                        </div>
                        
                        <div class="d-flex gap-2">
                            <a href="{{ route('properties.show', $property) }}" class="btn btn-luxury-primary flex-fill">View Details</a>
                            <button class="btn btn-luxury-secondary" data-bs-toggle="modal" data-bs-target="#inquiryModal" data-property-id="{{ $property->id }}" data-property-title="{{ $property->title }}">
                                Inquire
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            @empty
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-home fa-3x text-muted mb-3"></i>
                    <h4>No Properties Found</h4>
                    <p class="text-muted">Try adjusting your search filters to find more properties.</p>
                    <a href="{{ route('properties.index') }}" class="btn btn-luxury-primary">View All Properties</a>
                </div>
            </div>
            @endforelse
        </div>
        
        <!-- Pagination -->
        @if($properties->hasPages())
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-center">
                    {{ $properties->appends(request()->query())->links() }}
                </div>
            </div>
        </div>
        @endif
    </div>
</section>

<!-- Quick Inquiry Modal -->
<div class="modal fade" id="inquiryModal" tabindex="-1" aria-labelledby="inquiryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="inquiryModalLabel">Property Inquiry</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('contact.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <input type="hidden" name="property_id" id="modal-property-id">
                    <input type="hidden" name="type" value="property">
                    
                    <div class="mb-3">
                        <label for="modal-name" class="form-label">Name *</label>
                        <input type="text" class="form-control" id="modal-name" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="modal-email" class="form-label">Email *</label>
                        <input type="email" class="form-control" id="modal-email" name="email" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="modal-phone" class="form-label">Phone</label>
                        <input type="tel" class="form-control" id="modal-phone" name="phone">
                    </div>
                    
                    <div class="mb-3">
                        <label for="modal-message" class="form-label">Message *</label>
                        <textarea class="form-control" id="modal-message" name="message" rows="4" required placeholder="I'm interested in this property. Please provide more details."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-luxury-primary">Send Inquiry</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle inquiry modal
    const inquiryModal = document.getElementById('inquiryModal');
    if (inquiryModal) {
        inquiryModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const propertyId = button.getAttribute('data-property-id');
            const propertyTitle = button.getAttribute('data-property-title');
            
            const modalPropertyId = inquiryModal.querySelector('#modal-property-id');
            const modalMessage = inquiryModal.querySelector('#modal-message');
            
            modalPropertyId.value = propertyId;
            modalMessage.value = `I'm interested in "${propertyTitle}". Please provide more details.`;
        });
    }
    
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
@endpush
