@extends('layouts.app')

@section('title', $property->meta_title ?: $property->title . ' - Luxury Real Estate')
@section('description', $property->meta_description ?: $property->short_description)
@section('og_image', $property->featured_image_url)

@push('head')
<!-- Structured Data -->
<script type="application/ld+json">
{!! App\Helpers\SchemaHelper::generatePropertySchema($property) !!}
</script>

<!-- Breadcrumb Schema -->
<script type="application/ld+json">
{!! App\Helpers\SchemaHelper::generateBreadcrumbSchema([
    ['name' => 'Home', 'url' => route('home')],
    ['name' => 'Properties', 'url' => route('properties.index')],
    ['name' => $property->title, 'url' => route('properties.show', $property)]
]) !!}
</script>
@endpush

@section('content')
<!-- Property Hero Section -->
<section class="property-hero" style="padding-top: 100px;">
    <div class="container-fluid px-0">
        <div class="row g-0">
            <div class="col-lg-8">
                <!-- Main Image Gallery -->
                <div id="propertyCarousel" class="carousel slide" data-bs-ride="carousel">
                    <div class="carousel-inner">
                        <div class="carousel-item active">
                            <img src="{{ $property->featured_image_url }}" class="d-block w-100" alt="{{ $property->title }}" style="height: 500px; object-fit: cover;">
                        </div>
                        @foreach($property->images as $image)
                        <div class="carousel-item">
                            <img src="{{ asset('storage/' . $image->image_path) }}" class="d-block w-100" alt="{{ $image->alt_text }}" style="height: 500px; object-fit: cover;">
                        </div>
                        @endforeach
                    </div>
                    <button class="carousel-control-prev" type="button" data-bs-target="#propertyCarousel" data-bs-slide="prev">
                        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                        <span class="visually-hidden">Previous</span>
                    </button>
                    <button class="carousel-control-next" type="button" data-bs-target="#propertyCarousel" data-bs-slide="next">
                        <span class="carousel-control-next-icon" aria-hidden="true"></span>
                        <span class="visually-hidden">Next</span>
                    </button>
                    
                    <!-- Gallery Counter -->
                    <div class="position-absolute bottom-0 end-0 m-3">
                        <span class="badge bg-dark bg-opacity-75">
                            <i class="fas fa-images me-1"></i>
                            {{ $property->images->count() + 1 }} Photos
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <!-- Property Quick Info -->
                <div class="bg-white h-100 p-4 d-flex flex-column justify-content-center">
                    <div class="mb-3">
                        @if($property->is_featured)
                        <span class="badge bg-warning text-dark me-2">Featured</span>
                        @endif
                        @if($property->is_luxury)
                        <span class="badge" style="background: var(--primary-gold);">Luxury</span>
                        @endif
                        <span class="badge bg-success">{{ ucfirst($property->status) }}</span>
                    </div>
                    
                    <h1 class="heading-font mb-3">{{ $property->title }}</h1>
                    
                    <p class="text-muted mb-3">
                        <i class="fas fa-map-marker-alt me-2" style="color: var(--primary-gold);"></i>
                        {{ $property->address }}, {{ $property->city }}, {{ $property->state }}
                    </p>
                    
                    <div class="price-section mb-4">
                        <h2 class="text-primary mb-1" style="color: var(--primary-gold) !important;">
                            {{ $property->formatted_price }}
                        </h2>
                        <small class="text-muted">{{ ucfirst($property->price_type) }} Price</small>
                    </div>
                    
                    <!-- Key Features -->
                    <div class="row mb-4">
                        @if($property->bedrooms)
                        <div class="col-4 text-center">
                            <div class="feature-box">
                                <i class="fas fa-bed fa-2x mb-2" style="color: var(--primary-gold);"></i>
                                <div class="fw-bold">{{ $property->bedrooms }}</div>
                                <small class="text-muted">Bedrooms</small>
                            </div>
                        </div>
                        @endif
                        @if($property->bathrooms)
                        <div class="col-4 text-center">
                            <div class="feature-box">
                                <i class="fas fa-bath fa-2x mb-2" style="color: var(--primary-gold);"></i>
                                <div class="fw-bold">{{ $property->bathrooms }}</div>
                                <small class="text-muted">Bathrooms</small>
                            </div>
                        </div>
                        @endif
                        @if($property->area_sqft)
                        <div class="col-4 text-center">
                            <div class="feature-box">
                                <i class="fas fa-ruler-combined fa-2x mb-2" style="color: var(--primary-gold);"></i>
                                <div class="fw-bold">{{ number_format($property->area_sqft) }}</div>
                                <small class="text-muted">Sq Ft</small>
                            </div>
                        </div>
                        @endif
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="d-grid gap-2">
                        <button class="btn btn-luxury-primary" data-bs-toggle="modal" data-bs-target="#inquiryModal">
                            <i class="fas fa-envelope me-2"></i>Send Inquiry
                        </button>
                        <a href="{{ route('schedule-visit') }}?property_id={{ $property->id }}" class="btn btn-luxury-secondary">
                            <i class="fas fa-calendar me-2"></i>Schedule Visit
                        </a>
                        @if($property->virtual_tour_url)
                        <a href="{{ $property->virtual_tour_url }}" target="_blank" class="btn btn-outline-primary">
                            <i class="fas fa-vr-cardboard me-2"></i>Virtual Tour
                        </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Property Details -->
<section class="luxury-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <!-- Property Description -->
                <div class="property-section mb-5">
                    <h3 class="heading-font mb-3">About This Property</h3>
                    <div class="property-description">
                        {!! nl2br(e($property->description)) !!}
                    </div>
                </div>
                
                <!-- Property Details Grid -->
                <div class="property-section mb-5">
                    <h3 class="heading-font mb-3">Property Details</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-semibold">Property Type:</td>
                                    <td>{{ ucfirst($property->type) }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold">Status:</td>
                                    <td><span class="badge bg-success">{{ ucfirst($property->status) }}</span></td>
                                </tr>
                                @if($property->bedrooms)
                                <tr>
                                    <td class="fw-semibold">Bedrooms:</td>
                                    <td>{{ $property->bedrooms }}</td>
                                </tr>
                                @endif
                                @if($property->bathrooms)
                                <tr>
                                    <td class="fw-semibold">Bathrooms:</td>
                                    <td>{{ $property->bathrooms }}</td>
                                </tr>
                                @endif
                                @if($property->parking_spaces)
                                <tr>
                                    <td class="fw-semibold">Parking:</td>
                                    <td>{{ $property->parking_spaces }} spaces</td>
                                </tr>
                                @endif
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                @if($property->area_sqft)
                                <tr>
                                    <td class="fw-semibold">Area:</td>
                                    <td>{{ $property->formatted_area }}</td>
                                </tr>
                                @endif
                                @if($property->floor_number)
                                <tr>
                                    <td class="fw-semibold">Floor:</td>
                                    <td>{{ $property->floor_number }}{{ $property->total_floors ? ' of ' . $property->total_floors : '' }}</td>
                                </tr>
                                @endif
                                @if($property->year_built)
                                <tr>
                                    <td class="fw-semibold">Year Built:</td>
                                    <td>{{ $property->year_built }}</td>
                                </tr>
                                @endif
                                <tr>
                                    <td class="fw-semibold">Price Type:</td>
                                    <td>{{ ucfirst($property->price_type) }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold">Views:</td>
                                    <td>{{ number_format($property->views_count) }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Amenities -->
                @if($property->amenities && count($property->amenities) > 0)
                <div class="property-section mb-5">
                    <h3 class="heading-font mb-3">Amenities</h3>
                    <div class="row">
                        @foreach($property->amenities as $amenity)
                        <div class="col-md-6 col-lg-4 mb-2">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle me-2" style="color: var(--emerald-green);"></i>
                                <span>{{ $amenity }}</span>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
                
                <!-- Nearby Places -->
                @if($property->nearby_places && count($property->nearby_places) > 0)
                <div class="property-section mb-5">
                    <h3 class="heading-font mb-3">Nearby Places</h3>
                    <div class="row">
                        @foreach($property->nearby_places as $place)
                        <div class="col-md-6 mb-2">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-map-marker-alt me-2" style="color: var(--primary-gold);"></i>
                                <span>{{ $place }}</span>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
                
                <!-- Floor Plans -->
                @if($property->floor_plans && count($property->floor_plans) > 0)
                <div class="property-section mb-5">
                    <h3 class="heading-font mb-3">Floor Plans</h3>
                    <div class="row">
                        @foreach($property->floor_plans as $index => $floorPlan)
                        <div class="col-md-6 mb-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-file-pdf fa-3x mb-3" style="color: var(--wine-red);"></i>
                                    <h6>Floor Plan {{ $index + 1 }}</h6>
                                    <a href="{{ asset('storage/floor-plans/' . $floorPlan) }}" target="_blank" class="btn btn-sm btn-luxury-primary">
                                        <i class="fas fa-download me-1"></i>Download PDF
                                    </a>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
                
                <!-- Brochure Download -->
                @if($property->brochure_pdf)
                <div class="property-section mb-5">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <i class="fas fa-file-pdf fa-3x mb-3" style="color: var(--wine-red);"></i>
                            <h5>Property Brochure</h5>
                            <p class="text-muted">Download detailed information about this property</p>
                            <a href="{{ asset('storage/brochures/' . $property->brochure_pdf) }}" target="_blank" class="btn btn-luxury-primary">
                                <i class="fas fa-download me-2"></i>Download Brochure
                            </a>
                        </div>
                    </div>
                </div>
                @endif

                <!-- Location Map -->
                @if($property->latitude && $property->longitude)
                <div class="property-section mb-5">
                    <h3 class="heading-font mb-3">Location & Map</h3>
                    <div class="card">
                        <div class="card-body p-0">
                            <div id="propertyMap" style="height: 400px; border-radius: 8px;"></div>
                        </div>
                        <div class="card-footer">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h6 class="mb-1">{{ $property->address }}</h6>
                                    <p class="text-muted mb-0">{{ $property->city }}, {{ $property->state }} {{ $property->postal_code }}</p>
                                </div>
                                <div class="col-md-4 text-md-end">
                                    <a href="https://www.google.com/maps/dir/?api=1&destination={{ $property->latitude }},{{ $property->longitude }}"
                                       target="_blank" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-directions me-1"></i>Get Directions
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
            </div>
            
            <div class="col-lg-4">
                <!-- Contact Agent Card -->
                <div class="card mb-4 sticky-top" style="top: 120px;">
                    <div class="card-header bg-primary text-white" style="background: var(--primary-gold) !important;">
                        <h5 class="mb-0">
                            <i class="fas fa-user-tie me-2"></i>Contact Agent
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <img src="{{ asset('images/agent-placeholder.jpg') }}" alt="Agent" class="rounded-circle mb-2" width="80" height="80">
                            <h6>Luxury Property Expert</h6>
                            <p class="text-muted small">Premium Real Estate Specialist</p>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <a href="tel:+919876543210" class="btn btn-luxury-primary">
                                <i class="fas fa-phone me-2"></i>Call Now
                            </a>
                            <a href="https://wa.me/919876543210?text=Hi, I'm interested in {{ $property->title }}" target="_blank" class="btn btn-success">
                                <i class="fab fa-whatsapp me-2"></i>WhatsApp
                            </a>
                            <button class="btn btn-luxury-secondary" data-bs-toggle="modal" data-bs-target="#inquiryModal">
                                <i class="fas fa-envelope me-2"></i>Send Message
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Property Stats -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">Property Statistics</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Views:</span>
                            <strong>{{ number_format($property->views_count) }}</strong>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Inquiries:</span>
                            <strong>{{ number_format($property->inquiries_count) }}</strong>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Listed:</span>
                            <strong>{{ $property->created_at->diffForHumans() }}</strong>
                        </div>
                    </div>
                </div>
                
                <!-- Share Property -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Share This Property</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex gap-2">
                            <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->url()) }}" target="_blank" class="btn btn-outline-primary flex-fill">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="https://twitter.com/intent/tweet?url={{ urlencode(request()->url()) }}&text={{ urlencode($property->title) }}" target="_blank" class="btn btn-outline-info flex-fill">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode(request()->url()) }}" target="_blank" class="btn btn-outline-primary flex-fill">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <button class="btn btn-outline-secondary flex-fill" onclick="copyToClipboard('{{ request()->url() }}')">
                                <i class="fas fa-link"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Properties -->
@if($relatedProperties->count() > 0)
<section class="luxury-section bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title">
                    <h3 class="heading-font">Similar Properties</h3>
                    <p class="lead">You might also be interested in these properties</p>
                </div>
            </div>
        </div>
        
        <div class="row">
            @foreach($relatedProperties as $relatedProperty)
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="property-card">
                    <div class="property-image">
                        <img src="{{ $relatedProperty->featured_image_url }}" alt="{{ $relatedProperty->title }}" class="img-fluid">
                        <div class="property-price">{{ $relatedProperty->formatted_price }}</div>
                    </div>
                    <div class="property-content">
                        <h5 class="property-title">
                            <a href="{{ route('properties.show', $relatedProperty) }}" class="text-decoration-none text-dark">
                                {{ $relatedProperty->title }}
                            </a>
                        </h5>
                        <p class="property-location">
                            <i class="fas fa-map-marker-alt me-2" style="color: var(--primary-gold);"></i>
                            {{ $relatedProperty->city }}, {{ $relatedProperty->state }}
                        </p>
                        <a href="{{ route('properties.show', $relatedProperty) }}" class="btn btn-luxury-primary w-100">View Details</a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Property Inquiry Modal -->
<div class="modal fade" id="inquiryModal" tabindex="-1" aria-labelledby="inquiryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="inquiryModalLabel">Inquire About: {{ $property->title }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('contact.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <input type="hidden" name="property_id" value="{{ $property->id }}">
                    <input type="hidden" name="type" value="property">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Name *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email *</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">Phone</label>
                            <input type="tel" class="form-control" id="phone" name="phone">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="budget_range" class="form-label">Budget Range</label>
                            <select class="form-select" id="budget_range" name="budget_range">
                                <option value="">Select Budget</option>
                                <option value="50L-1Cr">50 Lakh - 1 Crore</option>
                                <option value="1Cr-2Cr">1 Crore - 2 Crore</option>
                                <option value="2Cr-5Cr">2 Crore - 5 Crore</option>
                                <option value="5Cr+">5 Crore+</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="message" class="form-label">Message *</label>
                        <textarea class="form-control" id="message" name="message" rows="4" required>I'm interested in "{{ $property->title }}". Please provide more details about this property including availability, pricing, and viewing arrangements.</textarea>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="newsletter" name="newsletter_subscription" value="1">
                        <label class="form-check-label" for="newsletter">
                            Subscribe to our newsletter for latest property updates
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-luxury-primary">
                        <i class="fas fa-paper-plane me-2"></i>Send Inquiry
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- Google Maps API -->
<script async defer src="https://maps.googleapis.com/maps/api/js?key=YOUR_GOOGLE_MAPS_API_KEY&callback=initMap"></script>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        alert('Property link copied to clipboard!');
    });
}

// Initialize Google Map
function initMap() {
    @if($property->latitude && $property->longitude)
    const propertyLocation = { lat: {{ $property->latitude }}, lng: {{ $property->longitude }} };

    const map = new google.maps.Map(document.getElementById("propertyMap"), {
        zoom: 15,
        center: propertyLocation,
        styles: [
            {
                "featureType": "all",
                "elementType": "geometry.fill",
                "stylers": [{"weight": "2.00"}]
            },
            {
                "featureType": "all",
                "elementType": "geometry.stroke",
                "stylers": [{"color": "#9c9c9c"}]
            },
            {
                "featureType": "all",
                "elementType": "labels.text",
                "stylers": [{"visibility": "on"}]
            },
            {
                "featureType": "landscape",
                "elementType": "all",
                "stylers": [{"color": "#f2f2f2"}]
            },
            {
                "featureType": "landscape",
                "elementType": "geometry.fill",
                "stylers": [{"color": "#ffffff"}]
            },
            {
                "featureType": "landscape.man_made",
                "elementType": "geometry.fill",
                "stylers": [{"color": "#ffffff"}]
            },
            {
                "featureType": "poi",
                "elementType": "all",
                "stylers": [{"visibility": "off"}]
            },
            {
                "featureType": "road",
                "elementType": "all",
                "stylers": [{"saturation": -100}, {"lightness": 45}]
            },
            {
                "featureType": "road",
                "elementType": "geometry.fill",
                "stylers": [{"color": "#eeeeee"}]
            },
            {
                "featureType": "road",
                "elementType": "labels.text.fill",
                "stylers": [{"color": "#7b7b7b"}]
            },
            {
                "featureType": "road",
                "elementType": "labels.text.stroke",
                "stylers": [{"color": "#ffffff"}]
            },
            {
                "featureType": "road.highway",
                "elementType": "all",
                "stylers": [{"visibility": "simplified"}]
            },
            {
                "featureType": "road.arterial",
                "elementType": "labels.icon",
                "stylers": [{"visibility": "off"}]
            },
            {
                "featureType": "transit",
                "elementType": "all",
                "stylers": [{"visibility": "off"}]
            },
            {
                "featureType": "water",
                "elementType": "all",
                "stylers": [{"color": "#46bcec"}, {"visibility": "on"}]
            },
            {
                "featureType": "water",
                "elementType": "geometry.fill",
                "stylers": [{"color": "#c8d7d4"}]
            },
            {
                "featureType": "water",
                "elementType": "labels.text.fill",
                "stylers": [{"color": "#070707"}]
            },
            {
                "featureType": "water",
                "elementType": "labels.text.stroke",
                "stylers": [{"color": "#ffffff"}]
            }
        ]
    });

    // Custom marker icon
    const customMarker = {
        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
            <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                <circle cx="20" cy="20" r="18" fill="#D4AF37" stroke="#B8860B" stroke-width="2"/>
                <path d="M20 10 L25 15 L22 15 L22 25 L18 25 L18 15 L15 15 Z" fill="white"/>
            </svg>
        `),
        scaledSize: new google.maps.Size(40, 40),
        anchor: new google.maps.Point(20, 20)
    };

    const marker = new google.maps.Marker({
        position: propertyLocation,
        map: map,
        title: "{{ $property->title }}",
        icon: customMarker,
        animation: google.maps.Animation.DROP
    });

    const infoWindow = new google.maps.InfoWindow({
        content: `
            <div style="padding: 10px; max-width: 300px;">
                <h6 style="margin: 0 0 8px 0; color: #D4AF37;">{{ $property->title }}</h6>
                <p style="margin: 0 0 8px 0; font-size: 14px;">{{ $property->address }}</p>
                <p style="margin: 0 0 8px 0; font-size: 14px; color: #666;">{{ $property->city }}, {{ $property->state }}</p>
                <p style="margin: 0; font-size: 16px; font-weight: bold; color: #D4AF37;">{{ $property->formatted_price }}</p>
            </div>
        `
    });

    marker.addListener("click", () => {
        infoWindow.open(map, marker);
    });

    // Show info window by default
    infoWindow.open(map, marker);
    @endif
}

// Fallback if Google Maps fails to load
window.addEventListener('load', function() {
    setTimeout(function() {
        if (typeof google === 'undefined' && document.getElementById('propertyMap')) {
            document.getElementById('propertyMap').innerHTML = `
                <div class="d-flex align-items-center justify-content-center h-100 bg-light">
                    <div class="text-center">
                        <i class="fas fa-map-marked-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Interactive Map</h5>
                        <p class="text-muted">Google Maps integration will be available with API key</p>
                        <a href="https://www.google.com/maps/search/?api=1&query={{ $property->latitude }},{{ $property->longitude }}"
                           target="_blank" class="btn btn-outline-primary">
                            <i class="fas fa-external-link-alt me-2"></i>View on Google Maps
                        </a>
                    </div>
                </div>
            `;
        }
    }, 3000);
});
</script>
@endpush
