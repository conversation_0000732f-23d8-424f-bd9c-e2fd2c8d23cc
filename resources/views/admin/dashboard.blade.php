@extends('admin.layouts.app')

@section('title', 'Dashboard - Admin Panel')
@section('page-title', 'Dashboard')

@section('content')
<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="text-muted mb-1">Total Properties</h6>
                    <div class="stats-number">{{ $stats['total_properties'] }}</div>
                </div>
                <div>
                    <i class="fas fa-building fa-2x text-muted"></i>
                </div>
            </div>
            <div class="mt-2">
                <small class="text-success">
                    <i class="fas fa-eye me-1"></i>{{ $stats['published_properties'] }} Published
                </small>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="text-muted mb-1">Total Inquiries</h6>
                    <div class="stats-number">{{ $stats['total_inquiries'] }}</div>
                </div>
                <div>
                    <i class="fas fa-envelope fa-2x text-muted"></i>
                </div>
            </div>
            <div class="mt-2">
                <small class="text-warning">
                    <i class="fas fa-clock me-1"></i>{{ $stats['new_inquiries'] }} New
                </small>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="text-muted mb-1">Testimonials</h6>
                    <div class="stats-number">{{ $stats['total_testimonials'] }}</div>
                </div>
                <div>
                    <i class="fas fa-star fa-2x text-muted"></i>
                </div>
            </div>
            <div class="mt-2">
                <small class="text-success">
                    <i class="fas fa-check me-1"></i>{{ $stats['published_testimonials'] }} Published
                </small>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="text-muted mb-1">Blog Posts</h6>
                    <div class="stats-number">{{ $stats['total_blog_posts'] }}</div>
                </div>
                <div>
                    <i class="fas fa-blog fa-2x text-muted"></i>
                </div>
            </div>
            <div class="mt-2">
                <small class="text-success">
                    <i class="fas fa-eye me-1"></i>{{ $stats['published_blog_posts'] }} Published
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ route('admin.properties.create') }}" class="btn btn-luxury-primary w-100">
                            <i class="fas fa-plus me-2"></i>Add Property
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ route('admin.inquiries.index') }}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-envelope me-2"></i>View Inquiries
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ route('admin.testimonials.create') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-star me-2"></i>Add Testimonial
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ route('home') }}" target="_blank" class="btn btn-outline-info w-100">
                            <i class="fas fa-external-link-alt me-2"></i>View Website
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Inquiries -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-envelope me-2"></i>Recent Inquiries
                </h5>
                <a href="{{ route('admin.inquiries.index') }}" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                @forelse($recentInquiries as $inquiry)
                <div class="d-flex justify-content-between align-items-start mb-3 pb-3 border-bottom">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">{{ $inquiry->name }}</h6>
                        <p class="text-muted small mb-1">{{ $inquiry->email }}</p>
                        @if($inquiry->property)
                        <p class="text-muted small mb-1">
                            <i class="fas fa-building me-1"></i>{{ $inquiry->property->title }}
                        </p>
                        @endif
                        <p class="small mb-0">{{ Str::limit($inquiry->message, 80) }}</p>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-{{ $inquiry->status === 'new' ? 'warning' : ($inquiry->status === 'contacted' ? 'info' : 'success') }}">
                            {{ ucfirst($inquiry->status) }}
                        </span>
                        <div class="text-muted small mt-1">
                            {{ $inquiry->created_at->diffForHumans() }}
                        </div>
                    </div>
                </div>
                @empty
                <div class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>No inquiries yet</p>
                </div>
                @endforelse
            </div>
        </div>
    </div>
    
    <!-- Recent Properties -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-building me-2"></i>Recent Properties
                </h5>
                <a href="{{ route('admin.properties.index') }}" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                @forelse($recentProperties as $property)
                <div class="d-flex justify-content-between align-items-start mb-3 pb-3 border-bottom">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">{{ $property->title }}</h6>
                        <p class="text-muted small mb-1">
                            <i class="fas fa-map-marker-alt me-1"></i>{{ $property->city }}, {{ $property->state }}
                        </p>
                        <p class="text-muted small mb-1">
                            <i class="fas fa-tag me-1"></i>{{ $property->formatted_price }}
                        </p>
                        <div class="d-flex gap-1">
                            @if($property->is_featured)
                            <span class="badge bg-warning text-dark">Featured</span>
                            @endif
                            @if($property->is_luxury)
                            <span class="badge" style="background: var(--primary-gold);">Luxury</span>
                            @endif
                            <span class="badge bg-{{ $property->status === 'available' ? 'success' : 'secondary' }}">
                                {{ ucfirst($property->status) }}
                            </span>
                        </div>
                    </div>
                    <div class="text-end">
                        <div class="text-muted small">
                            {{ $property->created_at->diffForHumans() }}
                        </div>
                        <div class="mt-1">
                            <a href="{{ route('admin.properties.show', $property) }}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                </div>
                @empty
                <div class="text-center text-muted py-4">
                    <i class="fas fa-building fa-3x mb-3"></i>
                    <p>No properties yet</p>
                </div>
                @endforelse
            </div>
        </div>
    </div>
</div>

<!-- Performance Chart Placeholder -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>Performance Overview
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <div class="border-end">
                            <h4 class="text-primary">{{ $stats['featured_properties'] }}</h4>
                            <p class="text-muted mb-0">Featured Properties</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="border-end">
                            <h4 class="text-success">{{ $stats['published_properties'] }}</h4>
                            <p class="text-muted mb-0">Published Properties</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="border-end">
                            <h4 class="text-warning">{{ $stats['new_inquiries'] }}</h4>
                            <p class="text-muted mb-0">Pending Inquiries</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <h4 class="text-info">{{ $stats['published_testimonials'] }}</h4>
                        <p class="text-muted mb-0">Active Testimonials</p>
                    </div>
                </div>
                
                <div class="mt-4 text-center">
                    <p class="text-muted">
                        <i class="fas fa-info-circle me-2"></i>
                        Advanced analytics and charts will be available in the next update.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh stats every 30 seconds
    setInterval(function() {
        // This would typically make an AJAX call to refresh stats
        console.log('Stats refresh interval - implement AJAX call here');
    }, 30000);
});
</script>
@endpush
