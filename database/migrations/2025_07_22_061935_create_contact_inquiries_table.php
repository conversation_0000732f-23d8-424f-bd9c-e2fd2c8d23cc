<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contact_inquiries', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email');
            $table->string('phone')->nullable();
            $table->string('subject')->nullable();
            $table->text('message');

            // Inquiry Type
            $table->enum('type', ['general', 'property', 'schedule_visit', 'investment', 'nri_desk'])->default('general');
            $table->foreignId('property_id')->nullable()->constrained()->onDelete('set null');

            // Additional Info
            $table->string('budget_range')->nullable();
            $table->string('preferred_location')->nullable();
            $table->string('property_type')->nullable();
            $table->date('preferred_visit_date')->nullable();
            $table->time('preferred_visit_time')->nullable();

            // Status and Follow-up
            $table->enum('status', ['new', 'contacted', 'in_progress', 'closed', 'spam'])->default('new');
            $table->text('admin_notes')->nullable();
            $table->timestamp('contacted_at')->nullable();
            $table->string('source')->nullable(); // website, whatsapp, phone, etc.

            // Contact Preferences
            $table->boolean('newsletter_subscription')->default(false);
            $table->json('contact_preferences')->nullable(); // email, phone, whatsapp

            $table->timestamps();

            // Indexes
            $table->index(['type', 'status']);
            $table->index(['status', 'created_at']);
            $table->index('property_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contact_inquiries');
    }
};
