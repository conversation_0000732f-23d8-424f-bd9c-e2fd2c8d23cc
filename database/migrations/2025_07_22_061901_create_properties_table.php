<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('properties', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('description');
            $table->text('short_description')->nullable();

            // Property Details
            $table->enum('type', ['apartment', 'villa', 'penthouse', 'townhouse', 'commercial', 'plot']);
            $table->enum('status', ['available', 'sold', 'under_construction', 'coming_soon']);
            $table->decimal('price', 15, 2);
            $table->string('price_type')->default('fixed'); // fixed, negotiable, on_request
            $table->integer('bedrooms')->nullable();
            $table->integer('bathrooms')->nullable();
            $table->decimal('area_sqft', 10, 2)->nullable();
            $table->decimal('area_sqm', 10, 2)->nullable();
            $table->integer('parking_spaces')->default(0);
            $table->integer('floor_number')->nullable();
            $table->integer('total_floors')->nullable();
            $table->year('year_built')->nullable();

            // Location Details
            $table->string('address');
            $table->string('city');
            $table->string('state');
            $table->string('country')->default('India');
            $table->string('postal_code')->nullable();
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->string('location_highlights')->nullable(); // Near Metro, Sea Facing, etc.

            // Media
            $table->string('featured_image')->nullable();
            $table->string('video_url')->nullable();
            $table->string('virtual_tour_url')->nullable();
            $table->json('floor_plans')->nullable(); // Array of floor plan images
            $table->string('brochure_pdf')->nullable();

            // SEO & Marketing
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->string('meta_keywords')->nullable();
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_luxury')->default(false);
            $table->boolean('is_published')->default(true);

            // Additional Info
            $table->text('amenities')->nullable(); // JSON array of amenities
            $table->text('nearby_places')->nullable(); // JSON array of nearby places
            $table->integer('views_count')->default(0);
            $table->integer('inquiries_count')->default(0);

            $table->timestamps();

            // Indexes
            $table->index(['type', 'status']);
            $table->index(['city', 'state']);
            $table->index(['price', 'type']);
            $table->index(['is_featured', 'is_published']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('properties');
    }
};
