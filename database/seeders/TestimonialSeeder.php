<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Testimonial;

class TestimonialSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $testimonials = [
            [
                'client_name' => '<PERSON><PERSON>',
                'client_designation' => 'CEO',
                'client_company' => 'Tech Innovations Pvt Ltd',
                'client_image' => null,
                'testimonial' => 'LuxuryEstate helped me find the perfect penthouse for my family. Their attention to detail and personalized service exceeded all expectations. The property they recommended was exactly what we were looking for.',
                'rating' => 5,
                'property_purchased' => 'Luxury Penthouse with Ocean View',
                'video_url' => null,
                'is_featured' => true,
                'is_published' => true,
                'sort_order' => 1,
            ],
            [
                'client_name' => 'Priya Mehta',
                'client_designation' => 'Investment Banker',
                'client_company' => 'Goldman Sachs',
                'client_image' => null,
                'testimonial' => 'The team at LuxuryEstate is exceptional. They understood my investment goals and found properties that not only met my lifestyle needs but also offered excellent ROI potential. Highly recommended!',
                'rating' => 5,
                'property_purchased' => 'Modern Villa with Private Garden',
                'video_url' => null,
                'is_featured' => true,
                'is_published' => true,
                'sort_order' => 2,
            ],
            [
                'client_name' => 'Amit Patel',
                'client_designation' => 'Entrepreneur',
                'client_company' => 'Patel Industries',
                'client_image' => null,
                'testimonial' => 'Professional, knowledgeable, and trustworthy. LuxuryEstate made the entire property buying process smooth and transparent. Their market insights were invaluable in making the right decision.',
                'rating' => 5,
                'property_purchased' => 'Luxury Apartment in Prime Location',
                'video_url' => null,
                'is_featured' => true,
                'is_published' => true,
                'sort_order' => 3,
            ],
            [
                'client_name' => 'Sarah Johnson',
                'client_designation' => 'NRI Investor',
                'client_company' => 'Johnson Consulting',
                'client_image' => null,
                'testimonial' => 'As an NRI, I was concerned about investing in Indian real estate remotely. LuxuryEstate\'s NRI desk provided excellent support throughout the process, making it hassle-free and secure.',
                'rating' => 5,
                'property_purchased' => null,
                'video_url' => null,
                'is_featured' => false,
                'is_published' => true,
                'sort_order' => 4,
            ],
            [
                'client_name' => 'Dr. Vikram Singh',
                'client_designation' => 'Cardiologist',
                'client_company' => 'Apollo Hospitals',
                'client_image' => null,
                'testimonial' => 'The virtual tour feature was amazing! I could explore properties from my busy schedule. The quality of properties and the professional service made LuxuryEstate my go-to real estate partner.',
                'rating' => 5,
                'property_purchased' => null,
                'video_url' => null,
                'is_featured' => false,
                'is_published' => true,
                'sort_order' => 5,
            ],
        ];

        foreach ($testimonials as $testimonialData) {
            Testimonial::create($testimonialData);
        }
    }
}
