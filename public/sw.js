// Service Worker for LuxuryEstate
const CACHE_NAME = 'luxuryestate-v1.0.0';
const STATIC_CACHE_URLS = [
    '/',
    '/about',
    '/services',
    '/properties',
    '/contact',
    '/build/assets/app.css',
    '/build/assets/app.js',
    '/images/logo.png',
    '/images/og-image.jpg',
    'https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'
];

// Install event - cache static assets
self.addEventListener('install', function(event) {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(function(cache) {
                console.log('Service Worker: Caching static assets');
                return cache.addAll(STATIC_CACHE_URLS);
            })
            .catch(function(error) {
                console.log('Service Worker: Cache failed', error);
            })
    );
    self.skipWaiting();
});

// Activate event - clean up old caches
self.addEventListener('activate', function(event) {
    event.waitUntil(
        caches.keys().then(function(cacheNames) {
            return Promise.all(
                cacheNames.map(function(cacheName) {
                    if (cacheName !== CACHE_NAME) {
                        console.log('Service Worker: Deleting old cache', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
    self.clients.claim();
});

// Fetch event - serve from cache, fallback to network
self.addEventListener('fetch', function(event) {
    // Skip non-GET requests
    if (event.request.method !== 'GET') {
        return;
    }

    // Skip admin routes
    if (event.request.url.includes('/admin/')) {
        return;
    }

    // Skip API routes
    if (event.request.url.includes('/api/')) {
        return;
    }

    event.respondWith(
        caches.match(event.request)
            .then(function(cachedResponse) {
                // Return cached version if available
                if (cachedResponse) {
                    // For HTML pages, try to update cache in background
                    if (event.request.headers.get('accept').includes('text/html')) {
                        fetch(event.request)
                            .then(function(fetchResponse) {
                                if (fetchResponse.status === 200) {
                                    const responseClone = fetchResponse.clone();
                                    caches.open(CACHE_NAME)
                                        .then(function(cache) {
                                            cache.put(event.request, responseClone);
                                        });
                                }
                            })
                            .catch(function() {
                                // Network failed, but we have cache
                            });
                    }
                    return cachedResponse;
                }

                // Not in cache, fetch from network
                return fetch(event.request)
                    .then(function(fetchResponse) {
                        // Don't cache non-successful responses
                        if (!fetchResponse || fetchResponse.status !== 200 || fetchResponse.type !== 'basic') {
                            return fetchResponse;
                        }

                        // Clone the response
                        const responseToCache = fetchResponse.clone();

                        // Add to cache for future requests
                        caches.open(CACHE_NAME)
                            .then(function(cache) {
                                // Only cache GET requests
                                if (event.request.method === 'GET') {
                                    cache.put(event.request, responseToCache);
                                }
                            });

                        return fetchResponse;
                    })
                    .catch(function() {
                        // Network failed and no cache available
                        // Return offline page for HTML requests
                        if (event.request.headers.get('accept').includes('text/html')) {
                            return new Response(
                                `<!DOCTYPE html>
                                <html>
                                <head>
                                    <title>Offline - LuxuryEstate</title>
                                    <meta name="viewport" content="width=device-width, initial-scale=1">
                                    <style>
                                        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f8f9fa; }
                                        .offline-container { max-width: 400px; margin: 0 auto; }
                                        .offline-icon { font-size: 4rem; color: #6c757d; margin-bottom: 1rem; }
                                        h1 { color: #495057; margin-bottom: 1rem; }
                                        p { color: #6c757d; margin-bottom: 2rem; }
                                        .btn { background: #D4AF37; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
                                    </style>
                                </head>
                                <body>
                                    <div class="offline-container">
                                        <div class="offline-icon">📱</div>
                                        <h1>You're Offline</h1>
                                        <p>Please check your internet connection and try again.</p>
                                        <a href="/" class="btn" onclick="window.location.reload()">Try Again</a>
                                    </div>
                                </body>
                                </html>`,
                                {
                                    headers: {
                                        'Content-Type': 'text/html'
                                    }
                                }
                            );
                        }
                        
                        // For other requests, return a generic error
                        return new Response('Offline', { status: 503 });
                    });
            })
    );
});

// Background sync for form submissions
self.addEventListener('sync', function(event) {
    if (event.tag === 'contact-form-sync') {
        event.waitUntil(
            // Handle offline form submissions
            syncContactForms()
        );
    }
});

// Push notification handling
self.addEventListener('push', function(event) {
    if (event.data) {
        const data = event.data.json();
        const options = {
            body: data.body,
            icon: '/images/logo.png',
            badge: '/images/logo.png',
            vibrate: [100, 50, 100],
            data: {
                dateOfArrival: Date.now(),
                primaryKey: data.primaryKey || 1
            },
            actions: [
                {
                    action: 'explore',
                    title: 'View Property',
                    icon: '/images/icons/view.png'
                },
                {
                    action: 'close',
                    title: 'Close',
                    icon: '/images/icons/close.png'
                }
            ]
        };

        event.waitUntil(
            self.registration.showNotification(data.title, options)
        );
    }
});

// Notification click handling
self.addEventListener('notificationclick', function(event) {
    event.notification.close();

    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('/properties')
        );
    } else if (event.action === 'close') {
        // Just close the notification
    } else {
        // Default action - open the app
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

// Helper function to sync contact forms
function syncContactForms() {
    return new Promise(function(resolve) {
        // This would typically sync offline form submissions
        // For now, just resolve
        resolve();
    });
}

// Periodic background sync for cache updates
self.addEventListener('periodicsync', function(event) {
    if (event.tag === 'cache-update') {
        event.waitUntil(updateCache());
    }
});

function updateCache() {
    return caches.open(CACHE_NAME)
        .then(function(cache) {
            return cache.addAll(STATIC_CACHE_URLS);
        });
}
