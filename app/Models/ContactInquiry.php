<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ContactInquiry extends Model
{
    protected $fillable = [
        'name', 'email', 'phone', 'subject', 'message',
        'type', 'property_id', 'budget_range', 'preferred_location',
        'property_type', 'preferred_visit_date', 'preferred_visit_time',
        'status', 'admin_notes', 'contacted_at', 'source',
        'newsletter_subscription', 'contact_preferences'
    ];

    protected $casts = [
        'contact_preferences' => 'array',
        'newsletter_subscription' => 'boolean',
        'preferred_visit_date' => 'date',
        'preferred_visit_time' => 'datetime',
        'contacted_at' => 'datetime',
    ];

    public function property(): BelongsTo
    {
        return $this->belongsTo(Property::class);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }
}
