<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Testimonial extends Model
{
    protected $fillable = [
        'client_name', 'client_designation', 'client_company', 'client_image',
        'testimonial', 'rating', 'property_purchased', 'video_url',
        'is_featured', 'is_published', 'sort_order'
    ];

    protected $casts = [
        'is_featured' => 'boolean',
        'is_published' => 'boolean',
        'rating' => 'integer',
        'sort_order' => 'integer',
    ];

    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function getClientImageUrlAttribute()
    {
        return $this->client_image ? asset('storage/' . $this->client_image) : null;
    }
}
