<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Property extends Model
{
    protected $fillable = [
        'title', 'slug', 'description', 'short_description',
        'type', 'status', 'price', 'price_type',
        'bedrooms', 'bathrooms', 'area_sqft', 'area_sqm',
        'parking_spaces', 'floor_number', 'total_floors', 'year_built',
        'address', 'city', 'state', 'country', 'postal_code',
        'latitude', 'longitude', 'location_highlights',
        'featured_image', 'video_url', 'virtual_tour_url',
        'floor_plans', 'brochure_pdf',
        'meta_title', 'meta_description', 'meta_keywords',
        'is_featured', 'is_luxury', 'is_published',
        'amenities', 'nearby_places', 'views_count', 'inquiries_count'
    ];

    protected $casts = [
        'floor_plans' => 'array',
        'amenities' => 'array',
        'nearby_places' => 'array',
        'is_featured' => 'boolean',
        'is_luxury' => 'boolean',
        'is_published' => 'boolean',
        'price' => 'decimal:2',
        'area_sqft' => 'decimal:2',
        'area_sqm' => 'decimal:2',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
    ];

    // Relationships
    public function images(): HasMany
    {
        return $this->hasMany(PropertyImage::class)->orderBy('sort_order');
    }

    public function amenities(): BelongsToMany
    {
        return $this->belongsToMany(PropertyAmenity::class, 'property_amenity_pivot');
    }

    public function inquiries(): HasMany
    {
        return $this->hasMany(ContactInquiry::class);
    }

    // Scopes
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeLuxury($query)
    {
        return $query->where('is_luxury', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByCity($query, $city)
    {
        return $query->where('city', $city);
    }

    public function scopePriceRange($query, $min, $max)
    {
        return $query->whereBetween('price', [$min, $max]);
    }

    // Accessors
    public function formattedPrice(): Attribute
    {
        return Attribute::make(
            get: fn () => '₹' . number_format($this->price, 0, '.', ',')
        );
    }

    public function formattedArea(): Attribute
    {
        return Attribute::make(
            get: fn () => number_format($this->area_sqft, 0) . ' sq ft'
        );
    }

    public function shortDescription(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->attributes['short_description'] ?: \Str::limit(strip_tags($this->description), 150)
        );
    }

    public function featuredImageUrl(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->featured_image ? asset('storage/' . $this->featured_image) : asset('images/property-placeholder.jpg')
        );
    }

    // Helper methods
    public function incrementViews()
    {
        $this->increment('views_count');
    }

    public function incrementInquiries()
    {
        $this->increment('inquiries_count');
    }

    public function getRouteKeyName()
    {
        return 'slug';
    }
}
