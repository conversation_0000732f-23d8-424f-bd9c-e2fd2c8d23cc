<?php

namespace App\Helpers;

use App\Models\Property;
use App\Models\Testimonial;

class SchemaHelper
{
    public static function generatePropertySchema(Property $property)
    {
        $schema = [
            "@context" => "https://schema.org",
            "@type" => "RealEstateListing",
            "name" => $property->title,
            "description" => $property->description,
            "url" => route('properties.show', $property),
            "image" => [
                $property->featured_image_url
            ],
            "offers" => [
                "@type" => "Offer",
                "price" => $property->price,
                "priceCurrency" => "INR",
                "availability" => $property->status === 'available' ? "https://schema.org/InStock" : "https://schema.org/OutOfStock"
            ],
            "address" => [
                "@type" => "PostalAddress",
                "streetAddress" => $property->address,
                "addressLocality" => $property->city,
                "addressRegion" => $property->state,
                "addressCountry" => $property->country,
                "postalCode" => $property->postal_code
            ]
        ];

        // Add property details if available
        if ($property->bedrooms) {
            $schema["numberOfRooms"] = $property->bedrooms;
        }

        if ($property->bathrooms) {
            $schema["numberOfBathroomsTotal"] = $property->bathrooms;
        }

        if ($property->area_sqft) {
            $schema["floorSize"] = [
                "@type" => "QuantitativeValue",
                "value" => $property->area_sqft,
                "unitText" => "square feet"
            ];
        }

        if ($property->latitude && $property->longitude) {
            $schema["geo"] = [
                "@type" => "GeoCoordinates",
                "latitude" => $property->latitude,
                "longitude" => $property->longitude
            ];
        }

        // Add amenities
        if ($property->amenities && count($property->amenities) > 0) {
            $schema["amenityFeature"] = array_map(function($amenity) {
                return [
                    "@type" => "LocationFeatureSpecification",
                    "name" => $amenity
                ];
            }, $property->amenities);
        }

        return json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);
    }

    public static function generateOrganizationSchema()
    {
        $schema = [
            "@context" => "https://schema.org",
            "@type" => "RealEstateAgent",
            "name" => "LuxuryEstate",
            "description" => "Premium luxury real estate services with world-class properties and exceptional customer service.",
            "url" => route('home'),
            "logo" => asset('images/logo.png'),
            "image" => asset('images/og-image.jpg'),
            "telephone" => "+91-98765-43210",
            "email" => "<EMAIL>",
            "address" => [
                "@type" => "PostalAddress",
                "streetAddress" => "123 Luxury Avenue",
                "addressLocality" => "Mumbai",
                "addressRegion" => "Maharashtra",
                "addressCountry" => "India",
                "postalCode" => "400001"
            ],
            "geo" => [
                "@type" => "GeoCoordinates",
                "latitude" => 19.0760,
                "longitude" => 72.8777
            ],
            "openingHours" => [
                "Mo-Sa 09:00-19:00",
                "Su 10:00-17:00"
            ],
            "sameAs" => [
                "https://www.facebook.com/luxuryestate",
                "https://www.twitter.com/luxuryestate",
                "https://www.instagram.com/luxuryestate",
                "https://www.linkedin.com/company/luxuryestate"
            ],
            "serviceArea" => [
                [
                    "@type" => "City",
                    "name" => "Mumbai"
                ],
                [
                    "@type" => "City",
                    "name" => "Delhi"
                ],
                [
                    "@type" => "City",
                    "name" => "Bangalore"
                ],
                [
                    "@type" => "City",
                    "name" => "Pune"
                ]
            ]
        ];

        return json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);
    }

    public static function generateBreadcrumbSchema($breadcrumbs)
    {
        $listItems = [];
        foreach ($breadcrumbs as $index => $breadcrumb) {
            $listItems[] = [
                "@type" => "ListItem",
                "position" => $index + 1,
                "name" => $breadcrumb['name'],
                "item" => $breadcrumb['url']
            ];
        }

        $schema = [
            "@context" => "https://schema.org",
            "@type" => "BreadcrumbList",
            "itemListElement" => $listItems
        ];

        return json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);
    }
}
