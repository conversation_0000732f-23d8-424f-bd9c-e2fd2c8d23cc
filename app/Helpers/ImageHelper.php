<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

class ImageHelper
{
    /**
     * Generate responsive image HTML with lazy loading
     */
    public static function responsiveImage($imagePath, $alt = '', $class = '', $sizes = [])
    {
        if (!$imagePath) {
            return self::placeholderImage($alt, $class);
        }

        $defaultSizes = [
            'thumbnail' => 300,
            'medium' => 600,
            'large' => 1200,
            'xlarge' => 1920
        ];

        $sizes = array_merge($defaultSizes, $sizes);
        $baseUrl = asset('storage/' . $imagePath);
        
        // Generate srcset for different screen sizes
        $srcset = [];
        foreach ($sizes as $name => $width) {
            $srcset[] = $baseUrl . " {$width}w";
        }

        return sprintf(
            '<img src="%s" alt="%s" class="%s" loading="lazy" decoding="async" srcset="%s" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw">',
            $baseUrl,
            htmlspecialchars($alt),
            $class,
            implode(', ', $srcset)
        );
    }

    /**
     * Generate placeholder image
     */
    public static function placeholderImage($alt = '', $class = '', $width = 400, $height = 300)
    {
        $placeholderUrl = "https://via.placeholder.com/{$width}x{$height}/f8f9fa/6c757d?text=" . urlencode($alt ?: 'Property Image');
        
        return sprintf(
            '<img src="%s" alt="%s" class="%s" loading="lazy" decoding="async">',
            $placeholderUrl,
            htmlspecialchars($alt),
            $class
        );
    }

    /**
     * Generate WebP version of image if supported
     */
    public static function webpImage($imagePath, $alt = '', $class = '')
    {
        if (!$imagePath) {
            return self::placeholderImage($alt, $class);
        }

        $baseUrl = asset('storage/' . $imagePath);
        $webpUrl = str_replace(['.jpg', '.jpeg', '.png'], '.webp', $baseUrl);
        
        return sprintf(
            '<picture>
                <source srcset="%s" type="image/webp">
                <img src="%s" alt="%s" class="%s" loading="lazy" decoding="async">
            </picture>',
            $webpUrl,
            $baseUrl,
            htmlspecialchars($alt),
            $class
        );
    }

    /**
     * Optimize image for web
     */
    public static function optimizeImage($imagePath, $quality = 85)
    {
        if (!Storage::disk('public')->exists($imagePath)) {
            return false;
        }

        $fullPath = Storage::disk('public')->path($imagePath);
        
        try {
            $image = Image::make($fullPath);
            
            // Optimize based on file type
            $extension = pathinfo($imagePath, PATHINFO_EXTENSION);
            
            switch (strtolower($extension)) {
                case 'jpg':
                case 'jpeg':
                    $image->encode('jpg', $quality);
                    break;
                case 'png':
                    $image->encode('png');
                    break;
                case 'webp':
                    $image->encode('webp', $quality);
                    break;
            }
            
            // Resize if too large
            if ($image->width() > 1920) {
                $image->resize(1920, null, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });
            }
            
            $image->save($fullPath);
            return true;
            
        } catch (\Exception $e) {
            \Log::error('Image optimization failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate critical CSS for above-the-fold content
     */
    public static function generateCriticalCSS()
    {
        return '
        <style>
        /* Critical CSS for above-the-fold content */
        body{font-family:"Montserrat",sans-serif;margin:0;padding:0}
        .navbar-luxury{background:rgba(255,255,255,0.95);backdrop-filter:blur(10px);border-bottom:1px solid rgba(212,175,55,0.2);position:fixed;top:0;left:0;right:0;z-index:1030}
        .hero-section{min-height:100vh;position:relative;display:flex;align-items:center;background:linear-gradient(135deg,rgba(0,0,0,0.4),rgba(0,0,0,0.6))}
        .hero-title{font-size:4rem;font-weight:300;color:white;font-family:"Didot","Times New Roman",serif}
        .btn-luxury-primary{background:linear-gradient(135deg,#D4AF37,#B8860B);border:none;color:white;padding:12px 30px;font-weight:600;text-transform:uppercase;letter-spacing:1px}
        </style>';
    }

    /**
     * Generate preload links for critical resources
     */
    public static function generatePreloadLinks()
    {
        $preloads = [
            ['href' => 'https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap', 'as' => 'style'],
            ['href' => asset('build/assets/app.css'), 'as' => 'style'],
            ['href' => asset('build/assets/app.js'), 'as' => 'script'],
        ];

        $html = '';
        foreach ($preloads as $preload) {
            $html .= sprintf('<link rel="preload" href="%s" as="%s">', $preload['href'], $preload['as']) . "\n";
        }

        return $html;
    }

    /**
     * Generate DNS prefetch links
     */
    public static function generateDNSPrefetch()
    {
        $domains = [
            'fonts.googleapis.com',
            'fonts.gstatic.com',
            'maps.googleapis.com',
            'cdnjs.cloudflare.com'
        ];

        $html = '';
        foreach ($domains as $domain) {
            $html .= sprintf('<link rel="dns-prefetch" href="//%s">', $domain) . "\n";
        }

        return $html;
    }
}
