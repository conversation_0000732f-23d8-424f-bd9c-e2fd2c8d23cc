<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class OptimizePerformance
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Add performance headers
        $response->headers->set('X-Frame-Options', 'SAMEORIGIN');
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');

        // Add caching headers for static assets
        if ($request->is('images/*') || $request->is('css/*') || $request->is('js/*')) {
            $response->headers->set('Cache-Control', 'public, max-age=31536000'); // 1 year
            $response->headers->set('Expires', gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');
        }

        // Add caching headers for pages
        if ($response->getStatusCode() === 200 && $request->isMethod('GET')) {
            if ($request->is('properties/*') || $request->is('blog/*')) {
                $response->headers->set('Cache-Control', 'public, max-age=3600'); // 1 hour
            } elseif ($request->is('/') || $request->is('about') || $request->is('services')) {
                $response->headers->set('Cache-Control', 'public, max-age=1800'); // 30 minutes
            }
        }

        // Minify HTML output for production
        if (app()->environment('production') && $response->headers->get('Content-Type') === 'text/html; charset=UTF-8') {
            $content = $response->getContent();

            // Remove unnecessary whitespace and comments
            $content = preg_replace('/<!--(?!<!)[^\[>].*?-->/s', '', $content);
            $content = preg_replace('/\s+/', ' ', $content);
            $content = preg_replace('/>\s+</', '><', $content);

            $response->setContent(trim($content));
        }

        return $response;
    }
}
