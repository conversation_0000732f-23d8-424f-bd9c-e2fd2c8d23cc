<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ContactInquiry;

class ContactController extends Controller
{
    public function index()
    {
        return view('contact.index');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'nullable|string|max:255',
            'message' => 'required|string',
            'property_id' => 'nullable|exists:properties,id',
        ]);

        ContactInquiry::create($validated);

        return redirect()->back()->with('success', 'Thank you for your inquiry. We will get back to you soon!');
    }

    public function scheduleVisit()
    {
        return view('contact.schedule-visit');
    }

    public function storeScheduleVisit(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'property_id' => 'nullable|exists:properties,id',
            'preferred_visit_date' => 'required|date|after:today',
            'preferred_visit_time' => 'required',
            'message' => 'nullable|string',
        ]);

        $validated['type'] = 'schedule_visit';
        ContactInquiry::create($validated);

        return redirect()->back()->with('success', 'Your visit has been scheduled. We will confirm the appointment shortly!');
    }
}
