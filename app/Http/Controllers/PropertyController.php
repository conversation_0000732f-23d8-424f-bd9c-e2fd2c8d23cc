<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Property;

class PropertyController extends Controller
{
    public function index(Request $request)
    {
        $query = Property::published()->with(['images']);

        // Apply filters
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('city')) {
            $query->where('city', $request->city);
        }

        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }

        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        if ($request->filled('bedrooms')) {
            if ($request->bedrooms == '4') {
                $query->where('bedrooms', '>=', 4);
            } else {
                $query->where('bedrooms', $request->bedrooms);
            }
        }

        // Apply sorting
        switch ($request->get('sort', 'newest')) {
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            case 'featured':
                $query->orderBy('is_featured', 'desc')->orderBy('created_at', 'desc');
                break;
            default: // newest
                $query->orderBy('created_at', 'desc');
                break;
        }

        // Always show featured properties first if no specific sorting
        if (!$request->filled('sort')) {
            $query->orderBy('is_featured', 'desc');
        }

        $properties = $query->paginate(12);

        return view('properties.index', compact('properties'));
    }

    public function show(Property $property)
    {
        $property->load(['images', 'amenities']);
        $property->incrementViews();

        // Get related properties
        $relatedProperties = Property::published()
            ->where('id', '!=', $property->id)
            ->where('city', $property->city)
            ->limit(3)
            ->get();

        return view('properties.show', compact('property', 'relatedProperties'));
    }
}
