<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Property;
use App\Models\BlogPost;
use Carbon\Carbon;

class SitemapController extends Controller
{
    public function index()
    {
        $sitemap = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $sitemap .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        // Static pages
        $staticPages = [
            ['url' => route('home'), 'priority' => '1.0', 'changefreq' => 'daily'],
            ['url' => route('about'), 'priority' => '0.8', 'changefreq' => 'monthly'],
            ['url' => route('services'), 'priority' => '0.8', 'changefreq' => 'monthly'],
            ['url' => route('contact'), 'priority' => '0.7', 'changefreq' => 'monthly'],
            ['url' => route('properties.index'), 'priority' => '0.9', 'changefreq' => 'daily'],
            ['url' => route('testimonials'), 'priority' => '0.6', 'changefreq' => 'weekly'],
            ['url' => route('virtual-tour'), 'priority' => '0.7', 'changefreq' => 'weekly'],
            ['url' => route('schedule-visit'), 'priority' => '0.7', 'changefreq' => 'monthly'],
            ['url' => route('blog.index'), 'priority' => '0.8', 'changefreq' => 'daily'],
        ];

        foreach ($staticPages as $page) {
            $sitemap .= $this->generateUrlEntry(
                $page['url'],
                Carbon::now()->toISOString(),
                $page['changefreq'],
                $page['priority']
            );
        }

        // Properties
        $properties = Property::published()->get();
        foreach ($properties as $property) {
            $sitemap .= $this->generateUrlEntry(
                route('properties.show', $property),
                $property->updated_at->toISOString(),
                'weekly',
                '0.8'
            );
        }

        // Blog posts
        $blogPosts = BlogPost::published()->get();
        foreach ($blogPosts as $post) {
            $sitemap .= $this->generateUrlEntry(
                route('blog.show', $post),
                $post->updated_at->toISOString(),
                'monthly',
                '0.6'
            );
        }

        $sitemap .= '</urlset>';

        return response($sitemap, 200, [
            'Content-Type' => 'application/xml'
        ]);
    }

    private function generateUrlEntry($url, $lastmod, $changefreq, $priority)
    {
        return "  <url>\n" .
               "    <loc>" . htmlspecialchars($url) . "</loc>\n" .
               "    <lastmod>" . $lastmod . "</lastmod>\n" .
               "    <changefreq>" . $changefreq . "</changefreq>\n" .
               "    <priority>" . $priority . "</priority>\n" .
               "  </url>\n";
    }

    public function robots()
    {
        $robots = "User-agent: *\n";
        $robots .= "Allow: /\n";
        $robots .= "Disallow: /admin/\n";
        $robots .= "Disallow: /storage/\n";
        $robots .= "\n";
        $robots .= "Sitemap: " . route('sitemap') . "\n";

        return response($robots, 200, [
            'Content-Type' => 'text/plain'
        ]);
    }
}
