<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Property;
use App\Models\PropertyImage;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class PropertyController extends Controller
{
    public function index()
    {
        $properties = Property::with(['images'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('admin.properties.index', compact('properties'));
    }

    public function create()
    {
        return view('admin.properties.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'type' => 'required|in:apartment,villa,penthouse,townhouse,commercial,plot',
            'status' => 'required|in:available,sold,under_construction,coming_soon',
            'price' => 'required|numeric|min:0',
            'price_type' => 'required|in:fixed,negotiable,on_request',
            'bedrooms' => 'nullable|integer|min:0',
            'bathrooms' => 'nullable|integer|min:0',
            'area_sqft' => 'nullable|numeric|min:0',
            'parking_spaces' => 'nullable|integer|min:0',
            'floor_number' => 'nullable|integer|min:0',
            'total_floors' => 'nullable|integer|min:0',
            'year_built' => 'nullable|integer|min:1900|max:' . (date('Y') + 5),
            'address' => 'required|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'country' => 'required|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'location_highlights' => 'nullable|string|max:255',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'video_url' => 'nullable|url',
            'virtual_tour_url' => 'nullable|url',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:255',
            'is_featured' => 'boolean',
            'is_luxury' => 'boolean',
            'is_published' => 'boolean',
            'amenities' => 'nullable|array',
            'nearby_places' => 'nullable|array',
        ]);

        // Generate slug
        $validated['slug'] = Str::slug($validated['title']);

        // Ensure unique slug
        $originalSlug = $validated['slug'];
        $counter = 1;
        while (Property::where('slug', $validated['slug'])->exists()) {
            $validated['slug'] = $originalSlug . '-' . $counter;
            $counter++;
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $validated['featured_image'] = $request->file('featured_image')->store('properties', 'public');
        }

        // Calculate area_sqm from area_sqft
        if ($validated['area_sqft']) {
            $validated['area_sqm'] = round($validated['area_sqft'] * 0.092903, 2);
        }

        $property = Property::create($validated);

        return redirect()->route('admin.properties.show', $property)
            ->with('success', 'Property created successfully!');
    }

    public function show(Property $property)
    {
        $property->load(['images', 'inquiries']);
        return view('admin.properties.show', compact('property'));
    }

    public function edit(Property $property)
    {
        return view('admin.properties.edit', compact('property'));
    }

    public function update(Request $request, Property $property)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'type' => 'required|in:apartment,villa,penthouse,townhouse,commercial,plot',
            'status' => 'required|in:available,sold,under_construction,coming_soon',
            'price' => 'required|numeric|min:0',
            'price_type' => 'required|in:fixed,negotiable,on_request',
            'bedrooms' => 'nullable|integer|min:0',
            'bathrooms' => 'nullable|integer|min:0',
            'area_sqft' => 'nullable|numeric|min:0',
            'parking_spaces' => 'nullable|integer|min:0',
            'floor_number' => 'nullable|integer|min:0',
            'total_floors' => 'nullable|integer|min:0',
            'year_built' => 'nullable|integer|min:1900|max:' . (date('Y') + 5),
            'address' => 'required|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'country' => 'required|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'location_highlights' => 'nullable|string|max:255',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'video_url' => 'nullable|url',
            'virtual_tour_url' => 'nullable|url',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:255',
            'is_featured' => 'boolean',
            'is_luxury' => 'boolean',
            'is_published' => 'boolean',
            'amenities' => 'nullable|array',
            'nearby_places' => 'nullable|array',
        ]);

        // Update slug if title changed
        if ($validated['title'] !== $property->title) {
            $validated['slug'] = Str::slug($validated['title']);

            // Ensure unique slug
            $originalSlug = $validated['slug'];
            $counter = 1;
            while (Property::where('slug', $validated['slug'])->where('id', '!=', $property->id)->exists()) {
                $validated['slug'] = $originalSlug . '-' . $counter;
                $counter++;
            }
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            // Delete old image
            if ($property->featured_image) {
                Storage::disk('public')->delete($property->featured_image);
            }
            $validated['featured_image'] = $request->file('featured_image')->store('properties', 'public');
        }

        // Calculate area_sqm from area_sqft
        if ($validated['area_sqft']) {
            $validated['area_sqm'] = round($validated['area_sqft'] * 0.092903, 2);
        }

        $property->update($validated);

        return redirect()->route('admin.properties.show', $property)
            ->with('success', 'Property updated successfully!');
    }

    public function destroy(Property $property)
    {
        // Delete featured image
        if ($property->featured_image) {
            Storage::disk('public')->delete($property->featured_image);
        }

        // Delete property images
        foreach ($property->images as $image) {
            Storage::disk('public')->delete($image->image_path);
        }

        $property->delete();

        return redirect()->route('admin.properties.index')
            ->with('success', 'Property deleted successfully!');
    }
}
