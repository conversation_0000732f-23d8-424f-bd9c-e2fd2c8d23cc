<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Property;
use App\Models\ContactInquiry;
use App\Models\Testimonial;
use App\Models\BlogPost;

class AdminController extends Controller
{
    public function dashboard()
    {
        $stats = [
            'total_properties' => Property::count(),
            'published_properties' => Property::published()->count(),
            'featured_properties' => Property::featured()->count(),
            'total_inquiries' => ContactInquiry::count(),
            'new_inquiries' => ContactInquiry::where('status', 'new')->count(),
            'total_testimonials' => Testimonial::count(),
            'published_testimonials' => Testimonial::published()->count(),
            'total_blog_posts' => BlogPost::count(),
            'published_blog_posts' => BlogPost::published()->count(),
        ];

        // Recent inquiries
        $recentInquiries = ContactInquiry::with('property')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Recent properties
        $recentProperties = Property::orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        return view('admin.dashboard', compact('stats', 'recentInquiries', 'recentProperties'));
    }
}
