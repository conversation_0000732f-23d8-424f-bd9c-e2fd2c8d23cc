<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Property;
use App\Models\Testimonial;
use App\Models\BlogPost;

class HomeController extends Controller
{
    public function index()
    {
        // Get featured properties
        $featuredProperties = Property::published()
            ->featured()
            ->with(['images'])
            ->limit(6)
            ->get();

        // Get luxury properties
        $luxuryProperties = Property::published()
            ->luxury()
            ->with(['images'])
            ->limit(3)
            ->get();

        // Get latest testimonials
        $testimonials = Testimonial::where('is_published', true)
            ->where('is_featured', true)
            ->orderBy('sort_order')
            ->limit(3)
            ->get();

        // Get latest blog posts
        $blogPosts = BlogPost::where('status', 'published')
            ->where('is_featured', true)
            ->orderBy('published_at', 'desc')
            ->limit(3)
            ->get();

        return view('home', compact(
            'featuredProperties',
            'luxuryProperties',
            'testimonials',
            'blogPosts'
        ));
    }

    public function about()
    {
        return view('pages.about');
    }

    public function services()
    {
        return view('pages.services');
    }

    public function virtualTour()
    {
        $properties = Property::published()
            ->whereNotNull('virtual_tour_url')
            ->with(['images'])
            ->get();

        return view('pages.virtual-tour', compact('properties'));
    }

    public function testimonials()
    {
        $testimonials = Testimonial::where('is_published', true)
            ->orderBy('sort_order')
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('pages.testimonials', compact('testimonials'));
    }
}
