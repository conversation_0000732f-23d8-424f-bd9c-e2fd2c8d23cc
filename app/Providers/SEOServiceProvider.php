<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\URL;

class SEOServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Force HTTPS in production
        if (app()->environment('production')) {
            URL::forceScheme('https');
        }

        // Share SEO data with all views
        View::composer('*', function ($view) {
            $view->with([
                'seoTitle' => config('app.name') . ' - Luxury Real Estate',
                'seoDescription' => 'Premium luxury real estate services with world-class properties and exceptional customer service.',
                'seoKeywords' => 'luxury real estate, premium properties, luxury homes, real estate investment, luxury apartments, villas, penthouses',
                'seoImage' => asset('images/og-image.jpg'),
                'seoUrl' => url()->current(),
            ]);
        });

        // Add global SEO meta tags
        View::composer('layouts.app', function ($view) {
            $view->with([
                'canonicalUrl' => url()->current(),
                'alternateUrls' => [
                    'en' => url()->current(),
                    // Add more languages as needed
                ],
            ]);
        });
    }
}
